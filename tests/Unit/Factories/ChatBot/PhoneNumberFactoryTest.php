<?php

namespace Tests\Unit\Factories\ChatBot;

use App\Factories\ChatBot\PhoneNumberFactory;
use App\Domains\ChatBot\PhoneNumber;
use App\Models\PhoneNumber as PhoneNumberModel;
use Tests\Unit\Factories\BaseFactoryTest;
use Illuminate\Foundation\Testing\RefreshDatabase;

class PhoneNumberFactoryTest extends BaseFactoryTest
{
    use RefreshDatabase;

    public function test_build_from_model()
    {
        $model = $this->createModelInstance();
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model);

        $this->assertInstanceOf(PhoneNumber::class, $domain);
        $this->assertEquals($model->id, $domain->id);
        $this->assertEquals($model->organization_id, $domain->organization_id);
        $this->assertEquals($model->user_id, $domain->user_id);
        $this->assertEquals($model->client_id, $domain->client_id);
        $this->assertEquals($model->flow_id, $domain->flow_id);
        $this->assertEquals($model->phone_number, $domain->phone_number);
        $this->assertEquals($model->name, $domain->name);
        $this->assertEquals($model->description, $domain->description);
        $this->assertEquals($model->is_active, $domain->is_active);
        $this->assertEquals($model->is_chatbot_activated, $domain->is_chatbot_activated);
        $this->assertEquals($model->whatsapp_phone_number_id, $domain->whatsapp_phone_number_id);
        $this->assertEquals($model->whatsapp_access_token, $domain->whatsapp_access_token);
        $this->assertEquals($model->created_at, $domain->created_at);
        $this->assertEquals($model->updated_at, $domain->updated_at);
        $this->assertEquals($model->deleted_at, $domain->deleted_at);
    }

    public function test_build_from_models_collection()
    {
        $models = collect([
            $this->createModelInstance(),
            $this->createModelInstance(),
            $this->createModelInstance(),
        ]);

        $factory = $this->createFactoryInstance();
        $domains = $factory->buildFromModels($models);

        $this->assertIsArray($domains);
        $this->assertCount(3, $domains);

        foreach ($domains as $domain) {
            $this->assertInstanceOf(PhoneNumber::class, $domain);
        }
    }

    public function test_build_from_models_with_empty_collection()
    {
        $factory = $this->createFactoryInstance();
        $domains = $factory->buildFromModels(collect());

        $this->assertNull($domains);
    }

    public function test_app_make_factory()
    {
        $factory = app()->make(PhoneNumberFactory::class);

        $this->assertInstanceOf(PhoneNumberFactory::class, $factory);
    }

    public function test_factory_can_handle_different_phone_number_types()
    {
        $regularPhone = PhoneNumberModel::factory()->create();
        $whatsappPhone = PhoneNumberModel::factory()->whatsappBusiness()->create();
        $customFormatPhone = PhoneNumberModel::factory()->withFormat('+55##########')->create();

        $factory = $this->createFactoryInstance();

        $regularDomain = $factory->buildFromModel($regularPhone);
        $whatsappDomain = $factory->buildFromModel($whatsappPhone);
        $customDomain = $factory->buildFromModel($customFormatPhone);

        $this->assertInstanceOf(PhoneNumber::class, $regularDomain);
        $this->assertInstanceOf(PhoneNumber::class, $whatsappDomain);
        $this->assertInstanceOf(PhoneNumber::class, $customDomain);

        $this->assertStringContainsString('WhatsApp Business', $whatsappDomain->name);
        $this->assertStringStartsWith('+55', $customDomain->phone_number);
    }

    public function test_factory_handles_inactive_phone_numbers()
    {
        $inactivePhone = PhoneNumberModel::factory()->create(['is_active' => false]);

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($inactivePhone);

        $this->assertInstanceOf(PhoneNumber::class, $domain);
        $this->assertFalse($domain->is_active);
    }

    public function test_factory_handles_chatbot_deactivated_phone_numbers()
    {
        $chatbotDeactivatedPhone = PhoneNumberModel::factory()->create(['is_chatbot_activated' => false]);

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($chatbotDeactivatedPhone);

        $this->assertInstanceOf(PhoneNumber::class, $domain);
        $this->assertFalse($domain->is_chatbot_activated);
    }

    public function test_build_from_store_request_sets_default_chatbot_activated()
    {
        $request = $this->createStoreRequest();
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromStoreRequest($request);

        $this->assertInstanceOf(PhoneNumber::class, $domain);
        $this->assertTrue($domain->is_chatbot_activated);
    }

    public function test_build_from_store_request_respects_chatbot_activated_value()
    {
        $request = $this->createStoreRequest();
        $request->merge(['is_chatbot_activated' => false]);

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromStoreRequest($request);

        $this->assertInstanceOf(PhoneNumber::class, $domain);
        $this->assertFalse($domain->is_chatbot_activated);
    }

    protected function createFactoryInstance()
    {
        return new PhoneNumberFactory();
    }

    protected function getDomainClass(): string
    {
        return PhoneNumber::class;
    }

    protected function createModelInstance()
    {
        return PhoneNumberModel::factory()->create();
    }

    protected function createStoreRequest()
    {
        $request = new \App\Http\Requests\PhoneNumber\StoreRequest();
        $request->merge([
            'organization_id' => 1,
            'user_id' => 1,
            'client_id' => 1,
            'flow_id' => 1,
            'phone_number' => '+5511999999999',
            'name' => 'Test Phone',
            'description' => 'Test Description',
            'is_active' => true,
            'is_chatbot_activated' => true,
            'whatsapp_phone_number_id' => 'test_id',
            'whatsapp_access_token' => 'test_token',
        ]);
        return $request;
    }

    protected function createUpdateRequest()
    {
        $request = new \App\Http\Requests\PhoneNumber\UpdateRequest();
        $request->merge([
            'organization_id' => 1,
            'user_id' => 1,
            'client_id' => 1,
            'flow_id' => 1,
            'phone_number' => '+5511888888888',
            'name' => 'Updated Phone',
            'description' => 'Updated Description',
            'is_active' => false,
            'whatsapp_phone_number_id' => 'updated_id',
            'whatsapp_access_token' => 'updated_token',
        ]);
        return $request;
    }
}
