<?php

namespace Tests\Unit\Services\Meta\WhatsApp\ChatBot;

use Tests\TestCase;
use App\Domains\ChatBot\Step;
use App\Enums\StepType;

class ProcessFlowStepTest extends TestCase
{

    public function test_step_type_enum_processing_works()
    {
        // Test that step types are correctly identified
        $messageStep = new Step(
            1, 1, 1, 'test_step', 'message', StepType::MESSAGE, 1, 2, null,
            true, false, ['text' => 'Hello'], null, null, '{}'
        );

        $this->assertEquals(StepType::MESSAGE, $messageStep->step_type);
        $this->assertTrue($messageStep->step_type === StepType::MESSAGE);

        $interactiveStep = new Step(
            2, 1, 1, 'interactive_step', 'interactive', StepType::INTERACTIVE, 2, 3, 1,
            false, false, ['text' => 'Choose', 'buttons' => []], null, null, '{}'
        );

        $this->assertEquals(StepType::INTERACTIVE, $interactiveStep->step_type);
        $this->assertTrue($interactiveStep->step_type === StepType::INTERACTIVE);

        $inputStep = new Step(
            3, 1, 1, 'input_step', 'input', StepType::INPUT, 3, 4, 2,
            false, false, ['prompt' => 'Enter name', 'field_mapping' => 'client.name'], null, null, '{}'
        );

        $this->assertEquals(StepType::INPUT, $inputStep->step_type);
        $this->assertTrue($inputStep->step_type === StepType::INPUT);

        $commandStep = new Step(
            4, 1, 1, 'command_step', 'command', StepType::COMMAND, 4, 5, 3,
            false, false, ['command' => 'update_client'], null, null, '{}'
        );

        $this->assertEquals(StepType::COMMAND, $commandStep->step_type);
        $this->assertTrue($commandStep->step_type === StepType::COMMAND);
    }

    public function test_legacy_type_conversion_still_works()
    {
        // Test that legacy type field is still converted to enum
        $step = new Step(
            1, 1, 1, 'legacy_step', 'interactive', null, 1, 2, null,
            true, false, null, null, null, '{}', null, null, null
        );

        // Initially no step_type set
        $this->assertNull($step->step_type);

        // Call the conversion method
        $step->setStepTypeFromLegacyFields();

        // Should now have the correct enum value
        $this->assertEquals(StepType::INTERACTIVE, $step->step_type);
    }

    public function test_step_configuration_validation()
    {
        $messageStep = new Step(
            1, 1, 1, 'message_step', 'message', StepType::MESSAGE, 1, 2, null,
            true, false, ['text' => 'Hello World'], null, null, '{}'
        );

        $this->assertTrue($messageStep->step_type->validateConfiguration($messageStep->configuration));

        $inputStep = new Step(
            2, 1, 1, 'input_step', 'input', StepType::INPUT, 2, 3, 1,
            false, false, ['prompt' => 'Enter name', 'field_mapping' => 'client.name'], null, null, '{}'
        );

        $this->assertTrue($inputStep->step_type->validateConfiguration($inputStep->configuration));
    }

    public function test_step_default_configuration_generation()
    {
        $step = new Step(
            1, 1, 1, 'test_step', 'message', StepType::MESSAGE, 1, 2, null,
            true, false, null, null, null, '{}'
        );

        $step->setDefaultConfiguration();

        $this->assertNotNull($step->configuration);
        $this->assertArrayHasKey('text', $step->configuration);
    }
}
