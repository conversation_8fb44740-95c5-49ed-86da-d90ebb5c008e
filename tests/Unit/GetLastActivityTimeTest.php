<?php

namespace Tests\Unit;

use App\Domains\ChatBot\Conversation;
use App\Domains\ChatBot\Interaction;
use App\Repositories\InteractionRepository;
use App\UseCases\ChatBot\Conversation\GetLastActivityTime;
use Carbon\Carbon;
use PHPUnit\Framework\TestCase;
use Mockery;

class GetLastActivityTimeTest extends TestCase
{
    public function test_should_return_last_interaction_time_when_interaction_exists(): void
    {
        // Arrange
        $interactionRepo = Mockery::mock(InteractionRepository::class);
        $useCase = new GetLastActivityTime($interactionRepo);

        $conversation = new Conversation(
            id: 1,
            created_at: Carbon::now()->subHours(3),
            updated_at: Carbon::now()->subHours(2)
        );

        $lastInteractionTime = Carbon::now()->subHour();
        $lastInteraction = new Interaction(
            id: 1,
            conversation_id: 1,
            created_at: $lastInteractionTime
        );

        $interactionRepo->shouldReceive('getLastByConversationId')
            ->with(1)
            ->once()
            ->andReturn($lastInteraction);

        // Act
        $result = $useCase->perform($conversation);

        // Assert
        $this->assertEquals($lastInteractionTime, $result);
    }

    public function test_should_return_conversation_updated_at_when_no_interaction(): void
    {
        // Arrange
        $interactionRepo = Mockery::mock(InteractionRepository::class);
        $useCase = new GetLastActivityTime($interactionRepo);

        $conversationUpdatedAt = Carbon::now()->subHours(2);
        $conversation = new Conversation(
            id: 1,
            created_at: Carbon::now()->subHours(3),
            updated_at: $conversationUpdatedAt
        );

        $interactionRepo->shouldReceive('getLastByConversationId')
            ->with(1)
            ->once()
            ->andReturn(null);

        // Act
        $result = $useCase->perform($conversation);

        // Assert
        $this->assertEquals($conversationUpdatedAt, $result);
    }

    public function test_should_return_conversation_created_at_when_no_interaction_and_no_updated_at(): void
    {
        // Arrange
        $interactionRepo = Mockery::mock(InteractionRepository::class);
        $useCase = new GetLastActivityTime($interactionRepo);

        $conversationCreatedAt = Carbon::now()->subHours(3);
        $conversation = new Conversation(
            id: 1,
            created_at: $conversationCreatedAt,
            updated_at: null
        );

        $interactionRepo->shouldReceive('getLastByConversationId')
            ->with(1)
            ->once()
            ->andReturn(null);

        // Act
        $result = $useCase->perform($conversation);

        // Assert
        $this->assertEquals($conversationCreatedAt, $result);
    }

    public function test_should_return_current_time_when_no_interaction_and_no_timestamps(): void
    {
        // Arrange
        $interactionRepo = Mockery::mock(InteractionRepository::class);
        $useCase = new GetLastActivityTime($interactionRepo);

        $conversation = new Conversation(
            id: 1,
            created_at: null,
            updated_at: null
        );

        $interactionRepo->shouldReceive('getLastByConversationId')
            ->with(1)
            ->once()
            ->andReturn(null);

        // Act
        $result = $useCase->perform($conversation);

        // Assert - should return a Carbon instance
        $this->assertInstanceOf(Carbon::class, $result);
        // Should be a valid Carbon date
        $this->assertNotNull($result);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
