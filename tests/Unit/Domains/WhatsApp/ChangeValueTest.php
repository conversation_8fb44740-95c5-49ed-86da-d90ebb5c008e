<?php

namespace Tests\Unit\Domains\WhatsApp;

use Tests\TestCase;
use App\Domains\WhatsApp\ChangeValue;
use App\Domains\ChatBot\PhoneNumber;

class ChangeValueTest extends TestCase
{
    public function test_constructor_initializes_properties_correctly()
    {
        $data = [
            'metadata' => ['phone_number_id' => '123'],
            'messages' => [['id' => 'msg1']],
            'statuses' => [['id' => 'status1']],
            'contacts' => [['name' => 'John']]
        ];

        $changeValue = new ChangeValue($data);

        $this->assertEquals(['phone_number_id' => '123'], $changeValue->metadata);
        $this->assertEquals([['id' => 'msg1']], $changeValue->messages);
        $this->assertEquals([['id' => 'status1']], $changeValue->statuses);
        $this->assertEquals([['name' => 'John']], $changeValue->contacts);
        $this->assertEquals('status1', $changeValue->primaryWamId);
    }

    public function test_constructor_handles_missing_properties()
    {
        $data = ['metadata' => ['phone_number_id' => '123']];

        $changeValue = new ChangeValue($data);

        $this->assertEquals(['phone_number_id' => '123'], $changeValue->metadata);
        $this->assertEquals([], $changeValue->messages);
        $this->assertEquals([], $changeValue->statuses);
        $this->assertEquals([], $changeValue->contacts);
    }

    public function test_get_phone_number_id_returns_correct_value()
    {
        $data = ['metadata' => ['phone_number_id' => 'test_123']];
        $changeValue = new ChangeValue($data);

        $this->assertEquals('test_123', $changeValue->getPhoneNumberId());
    }

    public function test_get_phone_number_id_returns_null_when_missing()
    {
        $data = ['metadata' => []];
        $changeValue = new ChangeValue($data);

        $this->assertNull($changeValue->getPhoneNumberId());
    }

    public function test_has_messages_returns_true_when_messages_exist()
    {
        $data = ['messages' => [['id' => 'msg1']]];
        $changeValue = new ChangeValue($data);

        $this->assertTrue($changeValue->hasMessages());
    }

    public function test_has_messages_returns_false_when_no_messages()
    {
        $data = ['messages' => []];
        $changeValue = new ChangeValue($data);

        $this->assertFalse($changeValue->hasMessages());
    }

    public function test_has_statuses_returns_true_when_statuses_exist()
    {
        $data = ['statuses' => [['id' => 'status1']]];
        $changeValue = new ChangeValue($data);

        $this->assertTrue($changeValue->hasStatuses());
    }

    public function test_has_statuses_returns_false_when_no_statuses()
    {
        $data = ['statuses' => []];
        $changeValue = new ChangeValue($data);

        $this->assertFalse($changeValue->hasStatuses());
    }

    public function test_get_incoming_messages_filters_outgoing()
    {
        $phoneNumber = new PhoneNumber(
            id: 1,
            organization_id: 1,
            user_id: null,
            client_id: null,
            flow_id: null,
            phone_number: '+5511999999999',
            name: 'Test Phone',
            description: 'Test Description',
            is_active: true,
            whatsapp_phone_number_id: 'phone_123'
        );

        $data = [
            'messages' => [
                ['id' => 'msg1', 'from' => '+5511888888888'], // Incoming
                ['id' => 'msg2', 'from' => '5511999999999'],  // Outgoing (same as business)
                ['id' => 'msg3', 'from' => '+5511777777777'], // Incoming
            ]
        ];

        $changeValue = new ChangeValue($data);
        $incomingMessages = $changeValue->getIncomingMessages($phoneNumber);

        $this->assertCount(2, $incomingMessages);
        $this->assertEquals('msg1', $incomingMessages[0]['id']);
        $this->assertEquals('msg3', $incomingMessages[2]['id']);
    }

    public function test_is_outgoing_message_detects_business_messages()
    {
        $phoneNumber = new PhoneNumber(
            id: 1,
            organization_id: 1,
            user_id: null,
            client_id: null,
            flow_id: null,
            phone_number: '+55 11 99999-9999',
            name: 'Test Phone',
            description: 'Test Description',
            is_active: true,
            is_chatbot_activated: true,
            whatsapp_phone_number_id: 'phone_123'
        );

        $changeValue = new ChangeValue([]);

        // Test various formats
        $this->assertTrue($changeValue->isOutgoingMessage(['from' => '5511999999999'], $phoneNumber));
        $this->assertTrue($changeValue->isOutgoingMessage(['from' => '+5511999999999'], $phoneNumber));
        $this->assertFalse($changeValue->isOutgoingMessage(['from' => '5511888888888'], $phoneNumber));
    }

    public function test_extract_status_summary_returns_correct_data()
    {
        $data = [
            'statuses' => [
                ['id' => 'msg1', 'status' => 'delivered'],
                ['id' => 'msg2', 'status' => 'read'],
                ['id' => 'msg3', 'status' => 'delivered'],
                ['id' => 'msg4', 'status' => 'failed']
            ]
        ];

        $changeValue = new ChangeValue($data);
        $summary = $changeValue->extractStatusSummary();

        $this->assertEquals(4, $summary['total']);
        $this->assertEquals(2, $summary['by_status']['delivered']);
        $this->assertEquals(1, $summary['by_status']['read']);
        $this->assertEquals(1, $summary['by_status']['failed']);
        $this->assertEquals(['msg1', 'msg2', 'msg3', 'msg4'], $summary['message_ids']);
    }

    public function test_get_event_type_returns_correct_type()
    {
        $messageData = ['messages' => [['id' => 'msg1']]];
        $statusData = ['statuses' => [['id' => 'status1']]];
        $emptyData = [];

        $this->assertEquals('message', (new ChangeValue($messageData))->getEventType());
        $this->assertEquals('status', (new ChangeValue($statusData))->getEventType());
        $this->assertEquals('other', (new ChangeValue($emptyData))->getEventType());
    }

    public function test_to_array_returns_all_properties()
    {
        $data = [
            'metadata' => ['phone_number_id' => '123'],
            'messages' => [['id' => 'msg1']],
            'statuses' => [['id' => 'status1']],
            'contacts' => [['name' => 'John']]
        ];

        $changeValue = new ChangeValue($data);
        $result = $changeValue->toArray();

        $this->assertEquals($data, $result);
    }

    public function test_validate_message_change_returns_true_for_valid_data()
    {
        $data = [
            'metadata' => ['phone_number_id' => '123'],
            'messages' => [
                [
                    'id' => 'msg1',
                    'from' => '+5511999999999',
                    'timestamp' => '1234567890',
                    'type' => 'text'
                ]
            ]
        ];

        $changeValue = new ChangeValue($data);

        $this->assertTrue($changeValue->validateMessageChange());
    }

    public function test_validate_message_change_returns_false_for_missing_phone_number_id()
    {
        $data = [
            'metadata' => [],
            'messages' => [
                [
                    'id' => 'msg1',
                    'from' => '+5511999999999',
                    'timestamp' => '1234567890',
                    'type' => 'text'
                ]
            ]
        ];

        $changeValue = new ChangeValue($data);

        $this->assertFalse($changeValue->validateMessageChange());
    }

    public function test_validate_message_change_returns_false_for_no_messages_or_statuses()
    {
        $data = [
            'metadata' => ['phone_number_id' => '123']
        ];

        $changeValue = new ChangeValue($data);

        $this->assertFalse($changeValue->validateMessageChange());
    }

    public function test_validate_message_change_returns_false_for_invalid_message_structure()
    {
        $data = [
            'metadata' => ['phone_number_id' => '123'],
            'messages' => [
                [
                    'id' => 'msg1',
                    // Missing required fields: from, timestamp, type
                ]
            ]
        ];

        $changeValue = new ChangeValue($data);

        $this->assertFalse($changeValue->validateMessageChange());
    }

    public function test_extracts_primary_wam_id_from_statuses()
    {
        $changeValueData = [
            'metadata' => ['phone_number_id' => 'phone_123'],
            'statuses' => [
                [
                    'id' => 'wamid.first_status',
                    'status' => 'delivered',
                    'timestamp' => '1234567890',
                    'recipient_id' => '+5511888888888'
                ],
                [
                    'id' => 'wamid.second_status',
                    'status' => 'read',
                    'timestamp' => '1234567891',
                    'recipient_id' => '+5511888888888'
                ]
            ],
            'messages' => []
        ];

        $changeValue = new ChangeValue($changeValueData);

        $this->assertEquals('wamid.first_status', $changeValue->getPrimaryWamId());
        $this->assertEquals('wamid.first_status', $changeValue->primaryWamId);
    }

    public function test_extracts_primary_wam_id_from_messages_when_no_statuses()
    {
        $changeValueData = [
            'metadata' => ['phone_number_id' => 'phone_123'],
            'statuses' => [],
            'messages' => [
                [
                    'id' => 'wamid.message_id',
                    'from' => '+5511999999999',
                    'timestamp' => '1234567890',
                    'type' => 'text'
                ]
            ]
        ];

        $changeValue = new ChangeValue($changeValueData);

        $this->assertEquals('wamid.message_id', $changeValue->getPrimaryWamId());
        $this->assertEquals('wamid.message_id', $changeValue->primaryWamId);
    }

    public function test_returns_null_when_no_wam_id_available()
    {
        $changeValueData = [
            'metadata' => ['phone_number_id' => 'phone_123'],
            'statuses' => [],
            'messages' => []
        ];

        $changeValue = new ChangeValue($changeValueData);

        $this->assertNull($changeValue->getPrimaryWamId());
        $this->assertNull($changeValue->primaryWamId);
    }

    public function test_prioritizes_statuses_over_messages()
    {
        $changeValueData = [
            'metadata' => ['phone_number_id' => 'phone_123'],
            'statuses' => [
                [
                    'id' => 'wamid.status_id',
                    'status' => 'delivered',
                    'timestamp' => '1234567890',
                    'recipient_id' => '+5511888888888'
                ]
            ],
            'messages' => [
                [
                    'id' => 'wamid.message_id',
                    'from' => '+5511999999999',
                    'timestamp' => '1234567890',
                    'type' => 'text'
                ]
            ]
        ];

        $changeValue = new ChangeValue($changeValueData);

        $this->assertEquals('wamid.status_id', $changeValue->getPrimaryWamId());
        $this->assertEquals('wamid.status_id', $changeValue->primaryWamId);
    }
}
