<?php

namespace Tests\Unit;

use App\Domains\ChatBot\Conversation;
use App\Domains\ChatBot\Flow;
use App\Enums\ChatBot\FlowStatus;
use App\Repositories\ConversationRepository;
use App\UseCases\ChatBot\Conversation\Close;
use App\UseCases\ChatBot\Conversation\CloseInactiveConversation;
use App\UseCases\ChatBot\Conversation\GetLastActivityTime;
use Carbon\Carbon;
use PHPUnit\Framework\TestCase;
use Mockery;

class CloseInactiveConversationSimpleTest extends TestCase
{
    public function test_should_not_close_already_finished_conversation(): void
    {
        // Arrange
        $conversationRepo = Mockery::mock(ConversationRepository::class);
        $closeUseCase = Mockery::mock(Close::class);
        $getLastActivityTimeUseCase = Mockery::mock(GetLastActivityTime::class);

        $useCase = new CloseInactiveConversation($conversationRepo, $closeUseCase, $getLastActivityTimeUseCase);

        $conversation = new Conversation(
            id: 1,
            is_finished: true
        );

        // Act
        $result = $useCase->perform($conversation);

        // Assert
        $this->assertFalse($result);
    }

    public function test_should_not_close_conversation_without_flow(): void
    {
        // Arrange
        $conversationRepo = Mockery::mock(ConversationRepository::class);
        $closeUseCase = Mockery::mock(Close::class);
        $getLastActivityTimeUseCase = Mockery::mock(GetLastActivityTime::class);

        $useCase = new CloseInactiveConversation($conversationRepo, $closeUseCase, $getLastActivityTimeUseCase);

        $conversation = new Conversation(
            id: 1,
            is_finished: false,
            flow: null
        );

        // Act
        $result = $useCase->perform($conversation);

        // Assert
        $this->assertFalse($result);
    }

    public function test_should_close_inactive_conversation(): void
    {
        // Arrange
        $conversationRepo = Mockery::mock(ConversationRepository::class);
        $closeUseCase = Mockery::mock(Close::class);
        $getLastActivityTimeUseCase = Mockery::mock(GetLastActivityTime::class);

        $useCase = new CloseInactiveConversation($conversationRepo, $closeUseCase, $getLastActivityTimeUseCase);

        $flow = new Flow(
            id: 1,
            organization_id: 1,
            name: 'Test Flow',
            description: 'Test',
            steps_count: 1,
            json: '{}',
            inactivity_minutes: 60,
            status: FlowStatus::ACTIVE
        );

        $conversation = new Conversation(
            id: 1,
            organization_id: 1,
            is_finished: false,
            created_at: Carbon::now()->subHours(2),
            updated_at: Carbon::now()->subHours(2),
            flow: $flow
        );

        $oldActivityTime = Carbon::now()->subHours(2);

        // Mock GetLastActivityTime UseCase
        $getLastActivityTimeUseCase->shouldReceive('perform')
            ->with($conversation)
            ->once()
            ->andReturn($oldActivityTime);

        // Mock close use case
        $closeUseCase->shouldReceive('perform')
            ->with($conversation, $oldActivityTime, 60)
            ->once()
            ->andReturn($conversation);

        // Act
        $result = $useCase->perform($conversation);

        // Assert
        $this->assertTrue($result);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
