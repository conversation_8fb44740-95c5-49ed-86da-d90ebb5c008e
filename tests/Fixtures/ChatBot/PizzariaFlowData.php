<?php

namespace Tests\Fixtures\ChatBot;

use App\Enums\ChatBot\StepType;
use App\Enums\ChatBot\ComponentFormat;
use App\Enums\ChatBot\WhatsAppButtonType;
use App\Enums\ChatBot\FlowStatus;

class PizzariaFlowData
{
    /**
     * Get complete pizzaria flow JSON data for testing
     */
    public static function getCompleteFlowData(): array
    {
        return [
            'flow' => [
                'name' => 'Fluxo Pizzaria',
                'description' => 'Fluxo completo para pedidos de pizza com escolha de tamanho e sabores',
                'is_default_flow' => false,
                'inactivity_minutes' => 30,
                'ending_conversation_message' => 'Obrigado por escolher nossa pizzaria! Seu pedido foi registrado.',
                'version' => '1.0',
                'status' => FlowStatus::ACTIVE->value,
                'variables' => [
                    'pizza_size' => null,
                    'pizza_flavors' => [],
                    'total_price' => 0,
                    'client_name' => null
                ]
            ],
            'steps' => [
                // Step 0: Boas-vindas
                [
                    'step' => 'welcome',
                    'step_type' => StepType::INTERACTIVE->value,
                    'position' => 0,
                    'next_step' => 1,
                    'is_initial_step' => true,
                    'is_ending_step' => false,
                    'component' => [
                        'name' => 'Welcome Component',
                        'type' => 'interactive',
                        'text' => '🍕 Bem-vindo à nossa Pizzaria! {{client.name}}, vamos fazer seu pedido?',
                        'format' => ComponentFormat::TEXT->value,
                        'buttons' => [
                            [
                                'text' => 'Fazer Pedido',
                                'type' => WhatsAppButtonType::REPLY->value,
                                'internal_type' => 'navigation',
                                'internal_data' => 'next_step'
                            ]
                        ]
                    ]
                ],
                // Step 1: Escolha do tamanho
                [
                    'step' => 'choose_size',
                    'step_type' => StepType::INTERACTIVE->value,
                    'position' => 1,
                    'next_step' => 2,
                    'earlier_step' => 0,
                    'is_initial_step' => false,
                    'is_ending_step' => false,
                    'component' => [
                        'name' => 'Size Selection Component',
                        'type' => 'interactive',
                        'text' => 'Escolha o tamanho da sua pizza:',
                        'format' => ComponentFormat::TEXT->value,
                        'buttons' => [
                            [
                                'text' => 'P - R$ 25,00',
                                'type' => WhatsAppButtonType::REPLY->value,
                                'internal_type' => 'condition',
                                'internal_data' => 'size_p',
                                'callback_data' => json_encode(['size' => 'P', 'price' => 25.00])
                            ],
                            [
                                'text' => 'M - R$ 35,00',
                                'type' => WhatsAppButtonType::REPLY->value,
                                'internal_type' => 'condition',
                                'internal_data' => 'size_m',
                                'callback_data' => json_encode(['size' => 'M', 'price' => 35.00])
                            ],
                            [
                                'text' => 'G - R$ 45,00',
                                'type' => WhatsAppButtonType::REPLY->value,
                                'internal_type' => 'condition',
                                'internal_data' => 'size_g',
                                'callback_data' => json_encode(['size' => 'G', 'price' => 45.00])
                            ]
                        ]
                    ]
                ],
                // Step 2: Escolha do tamanho Família (separado para não exceder limite de 3 botões)
                [
                    'step' => 'choose_size_family',
                    'step_type' => StepType::INTERACTIVE->value,
                    'position' => 2,
                    'next_step' => 3,
                    'earlier_step' => 1,
                    'is_initial_step' => false,
                    'is_ending_step' => false,
                    'component' => [
                        'name' => 'Family Size Component',
                        'type' => 'interactive',
                        'text' => 'Ou escolha o tamanho Família:',
                        'format' => ComponentFormat::TEXT->value,
                        'buttons' => [
                            [
                                'text' => 'F - R$ 65,00',
                                'type' => WhatsAppButtonType::REPLY->value,
                                'internal_type' => 'condition',
                                'internal_data' => 'size_f',
                                'callback_data' => json_encode(['size' => 'F', 'price' => 65.00])
                            ],
                            [
                                'text' => 'Voltar',
                                'type' => WhatsAppButtonType::REPLY->value,
                                'internal_type' => 'navigation',
                                'internal_data' => 'previous_step'
                            ]
                        ]
                    ]
                ],
                // Step 3: Escolha do primeiro sabor
                [
                    'step' => 'choose_first_flavor',
                    'step_type' => StepType::INTERACTIVE->value,
                    'position' => 3,
                    'next_step' => 4,
                    'earlier_step' => 2,
                    'is_initial_step' => false,
                    'is_ending_step' => false,
                    'component' => [
                        'name' => 'First Flavor Component',
                        'type' => 'interactive',
                        'text' => 'Escolha o primeiro sabor:',
                        'format' => ComponentFormat::TEXT->value,
                        'buttons' => [
                            [
                                'text' => 'Calabresa',
                                'type' => WhatsAppButtonType::REPLY->value,
                                'internal_type' => 'condition',
                                'internal_data' => 'flavor_calabresa',
                                'callback_data' => json_encode(['flavor' => 'Calabresa'])
                            ],
                            [
                                'text' => 'Portuguesa',
                                'type' => WhatsAppButtonType::REPLY->value,
                                'internal_type' => 'condition',
                                'internal_data' => 'flavor_portuguesa',
                                'callback_data' => json_encode(['flavor' => 'Portuguesa'])
                            ],
                            [
                                'text' => 'Frango',
                                'type' => WhatsAppButtonType::REPLY->value,
                                'internal_type' => 'condition',
                                'internal_data' => 'flavor_frango',
                                'callback_data' => json_encode(['flavor' => 'Frango'])
                            ]
                        ]
                    ]
                ],
                // Step 4: Escolha do segundo sabor (opcional)
                [
                    'step' => 'choose_second_flavor',
                    'step_type' => StepType::INTERACTIVE->value,
                    'position' => 4,
                    'next_step' => 5,
                    'earlier_step' => 3,
                    'is_initial_step' => false,
                    'is_ending_step' => false,
                    'component' => [
                        'name' => 'Second Flavor Component',
                        'type' => 'interactive',
                        'text' => 'Deseja adicionar um segundo sabor? (Opcional)',
                        'format' => ComponentFormat::TEXT->value,
                        'buttons' => [
                            [
                                'text' => 'Chocolate',
                                'type' => WhatsAppButtonType::REPLY->value,
                                'internal_type' => 'condition',
                                'internal_data' => 'flavor_chocolate',
                                'callback_data' => json_encode(['flavor' => 'Chocolate'])
                            ],
                            [
                                'text' => 'Outro sabor',
                                'type' => WhatsAppButtonType::REPLY->value,
                                'internal_type' => 'navigation',
                                'internal_data' => 'choose_other_flavor'
                            ],
                            [
                                'text' => 'Não, obrigado',
                                'type' => WhatsAppButtonType::REPLY->value,
                                'internal_type' => 'navigation',
                                'internal_data' => 'skip_second_flavor'
                            ]
                        ]
                    ]
                ],
                // Step 5: Exibição do resumo e valor
                [
                    'step' => 'order_summary',
                    'step_type' => StepType::MESSAGE->value,
                    'position' => 5,
                    'next_step' => 6,
                    'earlier_step' => 4,
                    'is_initial_step' => false,
                    'is_ending_step' => false,
                    'component' => [
                        'name' => 'Order Summary Component',
                        'type' => 'message',
                        'text' => '📋 Resumo do seu pedido:\n\n🍕 Tamanho: {{pizza.size}}\n🎯 Sabores: {{pizza.flavors}}\n💰 Total: R$ {{pizza.total_price}}',
                        'format' => ComponentFormat::TEXT->value,
                        'parameters' => [
                            [
                                'type' => 'text',
                                'value' => 'pizza_size',
                                'placeholder' => 'Não informado',
                                'index' => 0
                            ],
                            [
                                'type' => 'text',
                                'value' => 'pizza_flavors',
                                'placeholder' => 'Nenhum sabor selecionado',
                                'index' => 1
                            ],
                            [
                                'type' => 'text',
                                'value' => 'total_price',
                                'placeholder' => '0,00',
                                'index' => 2
                            ]
                        ]
                    ]
                ],
                // Step 6: Confirmação do pedido
                [
                    'step' => 'order_confirmation',
                    'step_type' => StepType::INTERACTIVE->value,
                    'position' => 6,
                    'next_step' => 7,
                    'earlier_step' => 5,
                    'is_initial_step' => false,
                    'is_ending_step' => false,
                    'component' => [
                        'name' => 'Order Confirmation Component',
                        'type' => 'interactive',
                        'text' => 'Confirma o pedido?',
                        'format' => ComponentFormat::TEXT->value,
                        'buttons' => [
                            [
                                'text' => 'Confirmar',
                                'type' => WhatsAppButtonType::REPLY->value,
                                'internal_type' => 'command',
                                'internal_data' => 'confirm_order'
                            ],
                            [
                                'text' => 'Cancelar',
                                'type' => WhatsAppButtonType::REPLY->value,
                                'internal_type' => 'navigation',
                                'internal_data' => 'cancel_order'
                            ],
                            [
                                'text' => 'Modificar',
                                'type' => WhatsAppButtonType::REPLY->value,
                                'internal_type' => 'navigation',
                                'internal_data' => 'restart_flow'
                            ]
                        ]
                    ]
                ],
                // Step 7: Finalização
                [
                    'step' => 'order_finalized',
                    'step_type' => StepType::MESSAGE->value,
                    'position' => 7,
                    'is_initial_step' => false,
                    'is_ending_step' => true,
                    'component' => [
                        'name' => 'Order Finalized Component',
                        'type' => 'message',
                        'text' => '✅ Pedido confirmado com sucesso!\n\n{{client.name}}, sua pizza será preparada em aproximadamente 30 minutos.\n\nObrigado pela preferência! 🍕',
                        'format' => ComponentFormat::TEXT->value,
                        'parameters' => [
                            [
                                'type' => 'text',
                                'value' => 'client_name',
                                'placeholder' => 'Cliente',
                                'index' => 0
                            ]
                        ]
                    ]
                ]
            ]
        ];
    }

    /**
     * Get simplified flow data for basic testing
     */
    public static function getSimpleFlowData(): array
    {
        return [
            'flow' => [
                'name' => 'Fluxo Pizzaria Simples',
                'description' => 'Fluxo básico para testes',
                'version' => '1.0',
                'status' => FlowStatus::DRAFT->value
            ],
            'steps' => [
                [
                    'step' => 'welcome',
                    'step_type' => StepType::MESSAGE->value,
                    'position' => 0,
                    'next_step' => 1,
                    'is_initial_step' => true,
                    'is_ending_step' => false,
                    'component' => [
                        'name' => 'Welcome',
                        'type' => 'message',
                        'text' => 'Bem-vindo!',
                        'format' => ComponentFormat::TEXT->value
                    ]
                ],
                [
                    'step' => 'end',
                    'step_type' => StepType::MESSAGE->value,
                    'position' => 1,
                    'is_initial_step' => false,
                    'is_ending_step' => true,
                    'component' => [
                        'name' => 'End',
                        'type' => 'message',
                        'text' => 'Obrigado!',
                        'format' => ComponentFormat::TEXT->value
                    ]
                ]
            ]
        ];
    }

    /**
     * Get complete pizzaria flow data as raw JSON string for Postman testing
     */
    public static function getCompleteFlowDataAsRawJSON(): string
    {
        return json_encode(self::getCompleteFlowData(), JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    }

    /**
     * Get simple flow data as raw JSON string for Postman testing
     */
    public static function getSimpleFlowDataAsRawJSON(): string
    {
        return json_encode(self::getSimpleFlowData(), JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    }
}
