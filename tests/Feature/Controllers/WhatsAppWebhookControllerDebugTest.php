<?php

namespace Tests\Feature\Controllers;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Models\Organization;
use App\Models\PhoneNumber;

class WhatsAppWebhookControllerDebugTest extends TestCase
{
    use RefreshDatabase;

    public function test_webhook_verify_works()
    {
        // Arrange
        $token = 'test_token_123';
        $challenge = '**********';

        $organization = Organization::factory()->create([
            'whatsapp_webhook_verify_token' => $token,
            'is_active' => true,
            'is_suspended' => false,
        ]);

        // Act
        $response = $this->get('/api/whatsapp/webhook?' . http_build_query([
            'hub_mode' => 'subscribe',
            'hub_verify_token' => $token,
            'hub_challenge' => $challenge,
        ]));

        // Assert
        $response->assertStatus(200);
        $this->assertEquals((int) $challenge, $response->json());
    }

    public function test_webhook_handle_with_minimal_payload()
    {
        // Arrange
        $secret = 'test_webhook_secret_debug';
        config(['whatsapp.webhook_secret' => $secret]);
        config(['resend.api_key' => 'test_resend_api_key_for_tests']);

        $organization = Organization::factory()->create([
            'is_active' => true,
            'is_suspended' => false,
            'whatsapp_webhook_secret' => $secret,
        ]);

        $phoneNumber = PhoneNumber::factory()->create([
            'organization_id' => $organization->id,
            'whatsapp_phone_number_id' => 'debug_phone_123',
            'is_active' => true,
            'is_chatbot_activated' => false, // Disable chatbot to avoid complex processing
        ]);

        $webhookPayload = [
            'object' => 'whatsapp_business_account',
            'entry' => [
                [
                    'id' => 'business_debug',
                    'changes' => [
                        [
                            'field' => 'messages',
                            'value' => [
                                'metadata' => [
                                    'phone_number_id' => 'debug_phone_123'
                                ],
                                'messages' => [
                                    [
                                        'id' => 'debug_msg_123',
                                        'from' => '*************',
                                        'timestamp' => '**********',
                                        'type' => 'text',
                                        'text' => ['body' => 'Debug test message']
                                    ]
                                ],
                                'contacts' => [
                                    [
                                        'profile' => ['name' => 'Debug User'],
                                        'wa_id' => '*************'
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];

        $payload = json_encode($webhookPayload);
        $signature = 'sha256=' . hash_hmac('sha256', $payload, $secret);

        // Act
        $response = $this->postJson('/api/whatsapp/webhook', $webhookPayload, [
            'X-Hub-Signature-256' => $signature
        ]);

        // Debug: Print response if it fails
        if ($response->status() !== 200) {
            dump('Response Status: ' . $response->status());
            dump('Response Content: ' . $response->content());
        }

        // Assert
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'status',
            'processed',
            'results'
        ]);
    }

    public function test_webhook_handle_with_invalid_phone_number_id()
    {
        // Arrange
        $secret = 'test_webhook_secret_debug';
        config(['whatsapp.webhook_secret' => $secret]);
        config(['resend.api_key' => 'test_resend_api_key_for_tests']);

        $webhookPayload = [
            'object' => 'whatsapp_business_account',
            'entry' => [
                [
                    'id' => 'business_invalid',
                    'changes' => [
                        [
                            'field' => 'messages',
                            'value' => [
                                'metadata' => [
                                    'phone_number_id' => 'invalid_phone_id'
                                ],
                                'messages' => [
                                    [
                                        'id' => 'invalid_msg_123',
                                        'from' => '*************',
                                        'type' => 'text',
                                        'text' => ['body' => 'Invalid phone test']
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];

        $payload = json_encode($webhookPayload);
        $signature = 'sha256=' . hash_hmac('sha256', $payload, $secret);

        // Act
        $response = $this->postJson('/api/whatsapp/webhook', $webhookPayload, [
            'X-Hub-Signature-256' => $signature
        ]);

        // Debug: Print response if it fails
        if ($response->status() !== 200) {
            dump('Response Status: ' . $response->status());
            dump('Response Content: ' . $response->content());
        }

        // Assert - Should handle gracefully
        $response->assertStatus(200);
    }
}
