<?php

namespace App\Repositories\ASAAS;

use App\Domains\ASAAS\Organization;
use App\Domains\ASAAS\Client;
use App\Domains\ASAAS\Sale;
use App\Factories\ASAAS\OrganizationFactory;
use App\Factories\ASAAS\ClientFactory;
use App\Factories\ASAAS\SaleFactory;
use App\Models\Organization as OrganizationModel;
use App\Models\Client as ClientModel;
use App\Models\Sale as SaleModel;
use App\Services\ASAAS\Models\AsaasOrganization;
use App\Services\ASAAS\Models\AsaasClient;
use App\Services\ASAAS\Models\AsaasSale;
use App\Enums\SubscriptionStatus;
use App\Enums\PaymentStatus;

/**
 * Aggregator repository for ASAAS domains
 * Provides complex queries and integration-specific methods
 */
class AsaasRepository
{
    public function __construct(
        private OrganizationFactory $organizationFactory,
        private ClientFactory $clientFactory,
        private SaleFactory $saleFactory
    ) {}

    /**
     * Find organization with full ASAAS integration data
     */
    public function findOrganizationWithFullIntegration(int $organizationId): ?Organization
    {
        $model = OrganizationModel::with([
            'asaas',
            'clients.asaas',
            'sales.asaas'
        ])->find($organizationId);

        return $this->organizationFactory->buildFromModel($model);
    }

    /**
     * Find organization by ID with ASAAS data
     */
    public function findOrganizationById(int $organizationId): ?Organization
    {
        $model = OrganizationModel::with('asaas')->find($organizationId);
        return $this->organizationFactory->buildFromModel($model);
    }

    /**
     * Find all organizations with ASAAS integration
     */
    public function findOrganizationsWithAsaasIntegration(): array
    {
        $models = OrganizationModel::with('asaas')
            ->whereHas('asaas')
            ->get();

        return $models->map(fn($model) => 
            $this->organizationFactory->buildFromModel($model)
        )->toArray();
    }

    /**
     * Find organizations by subscription status
     */
    public function findOrganizationsBySubscriptionStatus(SubscriptionStatus $status): array
    {
        $models = OrganizationModel::with('asaas')
            ->whereHas('asaas', function($query) use ($status) {
                $query->where('subscription_status', $status);
            })
            ->get();

        return $models->map(fn($model) => 
            $this->organizationFactory->buildFromModel($model)
        )->toArray();
    }

    /**
     * Find organizations with active subscriptions
     */
    public function findOrganizationsWithActiveSubscriptions(): array
    {
        return $this->findOrganizationsBySubscriptionStatus(SubscriptionStatus::ACTIVE);
    }

    /**
     * Find organizations in courtesy period
     */
    public function findOrganizationsInCourtesy(): array
    {
        $models = OrganizationModel::with('asaas')
            ->whereHas('asaas', function($query) {
                $query->where('is_courtesy', true)
                      ->where(function($q) {
                          $q->whereNull('courtesy_expires_at')
                            ->orWhere('courtesy_expires_at', '>', now());
                      });
            })
            ->get();

        return $models->map(fn($model) => 
            $this->organizationFactory->buildFromModel($model)
        )->toArray();
    }

    /**
     * Find clients by organization with ASAAS data
     */
    public function findClientsByOrganization(int $organizationId): array
    {
        $models = ClientModel::with('asaas')
            ->where('organization_id', $organizationId)
            ->get();

        return $models->map(fn($model) => 
            $this->clientFactory->buildFromModel($model)
        )->toArray();
    }

    /**
     * Find clients needing ASAAS sync
     */
    public function findClientsNeedingAsaasSync(): array
    {
        $models = ClientModel::with('asaas')
            ->whereHas('asaas', function($query) {
                $query->where(function($q) {
                    $q->whereNull('asaas_synced_at')
                      ->orWhere('asaas_synced_at', '<', now()->subHours(1));
                });
            })
            ->get();

        return $models->map(fn($model) => 
            $this->clientFactory->buildFromModel($model)
        )->toArray();
    }

    /**
     * Find clients without ASAAS integration
     */
    public function findClientsWithoutAsaasIntegration(int $organizationId): array
    {
        $models = ClientModel::where('organization_id', $organizationId)
            ->whereDoesntHave('asaas')
            ->get();

        return $models->map(fn($model) => 
            $this->clientFactory->buildFromModel($model)
        )->toArray();
    }

    /**
     * Find sales by organization with ASAAS data
     */
    public function findSalesByOrganization(int $organizationId): array
    {
        $models = SaleModel::with(['asaas', 'client.asaas'])
            ->where('organization_id', $organizationId)
            ->get();

        return $models->map(fn($model) => 
            $this->saleFactory->buildFromModel($model)
        )->toArray();
    }

    /**
     * Find sales by payment status
     */
    public function findSalesByPaymentStatus(PaymentStatus $status, int $organizationId): array
    {
        $models = SaleModel::with(['asaas', 'client.asaas'])
            ->where('organization_id', $organizationId)
            ->whereHas('asaas', function($query) use ($status) {
                $query->where('payment_status', $status);
            })
            ->get();

        return $models->map(fn($model) => 
            $this->saleFactory->buildFromModel($model)
        )->toArray();
    }

    /**
     * Find sales needing ASAAS sync
     */
    public function findSalesNeedingAsaasSync(): array
    {
        $models = SaleModel::with(['asaas', 'client.asaas'])
            ->whereHas('asaas', function($query) {
                $query->where(function($q) {
                    $q->whereNull('asaas_synced_at')
                      ->orWhere('asaas_synced_at', '<', now()->subMinutes(30));
                });
            })
            ->get();

        return $models->map(fn($model) => 
            $this->saleFactory->buildFromModel($model)
        )->toArray();
    }

    /**
     * Find sales without ASAAS payment
     */
    public function findSalesWithoutAsaasPayment(int $organizationId): array
    {
        $models = SaleModel::where('organization_id', $organizationId)
            ->whereDoesntHave('asaas')
            ->get();

        return $models->map(fn($model) => 
            $this->saleFactory->buildFromModel($model)
        )->toArray();
    }

    /**
     * Get integration summary for organization
     */
    public function getIntegrationSummary(int $organizationId): array
    {
        $organization = $this->findOrganizationById($organizationId);
        
        if (!$organization) {
            return [
                'organization' => null,
                'has_integration' => false,
                'error' => 'Organization not found'
            ];
        }

        $clients = $this->findClientsByOrganization($organizationId);
        $sales = $this->findSalesByOrganization($organizationId);
        
        $clientsWithAsaas = array_filter($clients, fn($client) => $client->hasAsaasIntegration());
        $salesWithAsaas = array_filter($sales, fn($sale) => $sale->hasAsaasPayment());
        
        return [
            'organization' => $organization->getSubscriptionSummary(),
            'clients' => [
                'total' => count($clients),
                'with_asaas' => count($clientsWithAsaas),
                'without_asaas' => count($clients) - count($clientsWithAsaas),
                'sync_percentage' => count($clients) > 0 ? round((count($clientsWithAsaas) / count($clients)) * 100, 2) : 0,
            ],
            'sales' => [
                'total' => count($sales),
                'with_asaas' => count($salesWithAsaas),
                'without_asaas' => count($sales) - count($salesWithAsaas),
                'payment_percentage' => count($sales) > 0 ? round((count($salesWithAsaas) / count($sales)) * 100, 2) : 0,
            ],
            'integration_health' => $this->calculateIntegrationHealth($organization, $clients, $sales),
        ];
    }

    /**
     * Calculate integration health score
     */
    private function calculateIntegrationHealth(Organization $organization, array $clients, array $sales): array
    {
        $score = 0;
        $maxScore = 100;
        $issues = [];

        // Organization integration (40 points)
        if ($organization->hasAsaasIntegration()) {
            $score += 20;
            
            if ($organization->canAccessSystem()) {
                $score += 20;
            } else {
                $issues[] = 'Organization cannot access system (subscription/courtesy issues)';
            }
        } else {
            $issues[] = 'Organization does not have ASAAS integration';
        }

        // Client integration (30 points)
        if (count($clients) > 0) {
            $clientsWithAsaas = array_filter($clients, fn($client) => $client->hasAsaasIntegration());
            $clientPercentage = (count($clientsWithAsaas) / count($clients)) * 100;
            $score += ($clientPercentage / 100) * 30;
            
            if ($clientPercentage < 50) {
                $issues[] = "Low client integration rate: {$clientPercentage}%";
            }
        }

        // Sales integration (30 points)
        if (count($sales) > 0) {
            $salesWithAsaas = array_filter($sales, fn($sale) => $sale->hasAsaasPayment());
            $salesPercentage = (count($salesWithAsaas) / count($sales)) * 100;
            $score += ($salesPercentage / 100) * 30;
            
            if ($salesPercentage < 50) {
                $issues[] = "Low sales payment integration rate: {$salesPercentage}%";
            }
        }

        return [
            'score' => round($score, 2),
            'max_score' => $maxScore,
            'percentage' => round(($score / $maxScore) * 100, 2),
            'status' => $this->getHealthStatus($score),
            'issues' => $issues,
        ];
    }

    /**
     * Get health status based on score
     */
    private function getHealthStatus(float $score): string
    {
        if ($score >= 80) return 'excellent';
        if ($score >= 60) return 'good';
        if ($score >= 40) return 'fair';
        if ($score >= 20) return 'poor';
        return 'critical';
    }
}
