<?php

namespace App\Factories\ChatBot;

use App\Domains\ChatBot\Campaign;
use App\Factories\Inventory\ClientFactory;
use App\Http\Requests\Campaign\StoreRequest;
use App\Http\Requests\Campaign\UpdateRequest;
use App\Models\Campaign as CampaignModel;
use App\Enums\CampaignStatus;
use Carbon\Carbon;

class CampaignFactory
{

    public TemplateFactory $templateFactory;
    public MessageFactory $messageFactory;
    public ClientFactory $clientFactory;
    private PhoneNumberFactory $phoneNumberFactory;

    public function __construct(
        TemplateFactory $templateFactory,
        MessageFactory $messageFactory,
        ClientFactory $clientFactory,
        PhoneNumberFactory $phoneNumberFactory
    ) {
        $this->templateFactory = $templateFactory;
        $this->messageFactory = $messageFactory;
        $this->clientFactory = $clientFactory;
        $this->phoneNumberFactory = $phoneNumberFactory;
    }

    public function buildFromStoreRequest(StoreRequest $request): Campaign
    {
        return new Campaign(
            id: null,
            organization_id: $request->organization_id ?? null,
            user_id: $request->user_id ?? null,
            template_id: $request->template_id ?? null,
            phone_number_id: $request->phone_number_id ?? null,
            name: $request->name ?? null,
            description: $request->description ?? null,
            is_scheduled: $request->is_scheduled ?? false,
            is_sent: $request->is_sent ?? false,
            is_sending: $request->is_sending ?? false,
            is_direct_message: $request->is_direct_message ?? false,
            message_count: $request->message_count ?? 0,
            sent_at: isset($request->sent_at) ? Carbon::parse($request->sent_at) : null,
            scheduled_at: isset($request->scheduled_at) ? Carbon::parse($request->scheduled_at) : null,
        );
    }

    public function buildFromUpdateRequest(UpdateRequest $request) : Campaign {
        return new Campaign(
            id: null,
            organization_id: null,
            user_id: null,
            template_id: $request->template_id ?? null,
            phone_number_id: $request->phone_number_id ?? null,
            name: $request->name ?? null,
            description: $request->description ?? null,
            is_scheduled: $request->is_scheduled ?? false,
            is_sent: $request->is_sent ?? false,
            is_sending: $request->is_sending ?? false,
            is_direct_message: $request->is_direct_message ?? false,
            message_count: $request->message_count ?? 0,
            sent_at: isset($request->sent_at) ? Carbon::parse($request->sent_at) : null,
            scheduled_at: isset($request->scheduled_at) ? Carbon::parse($request->scheduled_at) : null,
        );
    }

    public function buildFromModel(
        ?CampaignModel $campaign,
        $with_template = true,
        $with_messages = false,
        $with_clients = false,
        $with_parameters = false,
        $with_phone_number = false,
        $with_categories = false,
        $with_tags = false,
        $with_status_history = false
    ): ?Campaign {
        if (!$campaign) {  return null; }

        if ($with_template) { $template = $this->templateFactory->buildFromModel($campaign->template ?? null); }
        if ($with_messages) { $messages = $this->messageFactory->buildFromModels($campaign->messages ?? null); }
        if ($with_clients) { $clients = $this->clientFactory->buildFromModels($campaign->clients ?? null); }
        if ($with_parameters) {
            $parameterFactory = app()->make(ParameterFactory::class);
            $parameters = $parameterFactory->buildFromModels($campaign->parameters ?? null, false, false);
        }
        if ($with_phone_number) { $phoneNumber = $this->phoneNumberFactory->buildFromModel($campaign->phoneNumber ?? null); }
        if ($with_categories) {
            $categoryFactory = app()->make(CategoryFactory::class);
            $categories = $categoryFactory->buildCollection($campaign->categories ?? [], false);
        }
        if ($with_tags) {
            $tagFactory = app()->make(TagFactory::class);
            $tags = $tagFactory->buildCollection($campaign->tags ?? [], false);
        }
        if ($with_status_history) {
            $statusHistoryFactory = app()->make(CampaignStatusHistoryFactory::class);
            $statusHistory = $statusHistoryFactory->buildCollection($campaign->statusHistory ?? [], false, false);
        }

        return new Campaign(
            $campaign->id ?? null,
            $campaign->organization_id ?? null,
            $campaign->user_id ?? null,
            $campaign->template_id ?? null,
            $campaign->phone_number_id ?? null,
            $campaign->name ?? null,
            $campaign->description ?? null,
            $campaign->is_scheduled ?? false,
            $campaign->is_sent ?? false,
            $campaign->is_sending ?? false,
            $campaign->is_direct_message ?? false,
            $campaign->message_count ?? 0,
            $campaign->status ?? CampaignStatus::DRAFT,
            $campaign->sent_at ? Carbon::parse($campaign->sent_at) : null,
            $campaign->scheduled_at ? Carbon::parse($campaign->scheduled_at) : null,
            $campaign->cancelled_at ? Carbon::parse($campaign->cancelled_at) : null,
            $campaign->failed_at ? Carbon::parse($campaign->failed_at) : null,
            $campaign->created_at ? Carbon::parse($campaign->created_at) : null,
            $campaign->updated_at ? Carbon::parse($campaign->updated_at) : null,
            $template ?? null,
            $phoneNumber ?? null,
            $messages ?? null,
            $clients ?? null,
            $parameters ?? null,
            $categories ?? null,
            $tags ?? null,
            $statusHistory ?? null
        );
    }
}
