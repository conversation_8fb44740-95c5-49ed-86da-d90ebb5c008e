<?php

namespace App\Factories\ChatBot;

use App\Domains\ChatBot\PhoneNumber;
use App\Http\Requests\PhoneNumber\StoreRequest;
use App\Http\Requests\PhoneNumber\UpdateRequest;
use App\Models\PhoneNumber as PhoneNumberModel;
use App\Factories\OrganizationFactory;
use Illuminate\Support\Collection;

class PhoneNumberFactory
{
    private ?OrganizationFactory $organizationFactory = null;
    public function buildFromStoreRequest(StoreRequest $request) : PhoneNumber {
        return new PhoneNumber(
            null,
            $request->organization_id ?? null,
            $request->user_id ?? null,
            $request->client_id ?? null,
            $request->flow_id ?? null,
            $request->phone_number ?? null,
            $request->name ?? null,
            $request->description ?? null,
            $request->is_active ?? true,
            $request->is_chatbot_activated ?? true,
            $request->whatsapp_phone_number_id ?? null,
            $request->whatsapp_business_id ?? null,
            $request->whatsapp_access_token ?? null,
        );
    }

    public function buildFromUpdateRequest(UpdateRequest $request) : PhoneNumber {
        return new PhoneNumber(
            null,
            $request->organization_id ?? null,
            $request->user_id ?? null,
            $request->client_id ?? null,
            $request->flow_id ?? null,
            $request->phone_number ?? null,
            $request->name ?? null,
            $request->description ?? null,
            $request->is_active ?? null,
            $request->is_chatbot_activated ?? null,
            $request->whatsapp_phone_number_id ?? null,
            $request->whatsapp_business_id ?? null,
            $request->whatsapp_access_token ?? null,
        );
    }

    public function buildFromModel(?PhoneNumberModel $phoneNumber, bool $with_organization = false) : ?PhoneNumber {
        if (!$phoneNumber){ return null; }

        $organization = null;
        if ($with_organization && $phoneNumber->organization) {
            if (!$this->organizationFactory) {
                $this->organizationFactory = new OrganizationFactory();
            }
            $organization = $this->organizationFactory->buildFromModel($phoneNumber->organization);
        }

        return new PhoneNumber(
            $phoneNumber->id ?? null,
            $phoneNumber->organization_id ?? null,
            $phoneNumber->user_id ?? null,
            $phoneNumber->client_id ?? null,
            $phoneNumber->flow_id ?? null,
            $phoneNumber->phone_number ?? null,
            $phoneNumber->name ?? null,
            $phoneNumber->description ?? null,
            $phoneNumber->is_active ?? null,
            $phoneNumber->is_chatbot_activated ?? null,
            $phoneNumber->whatsapp_phone_number_id ?? null,
            $phoneNumber->whatsapp_business_id ?? null,
            $phoneNumber->whatsapp_access_token ?? null,
            $phoneNumber->created_at ?? null,
            $phoneNumber->updated_at ?? null,
            $phoneNumber->deleted_at ?? null,
            $organization,
        );
    }

    /**
     * @param Collection|null $phoneNumbers
     * @return PhoneNumber[]|null
     */
    public function buildFromModels(?Collection $phoneNumbers) : ?array {
        if (!$phoneNumbers || $phoneNumbers->isEmpty()){ return null; }

        $domains = [];

        /** @var PhoneNumberModel $phoneNumber */
        foreach ($phoneNumbers as $phoneNumber){
            $domains[] = $this->buildFromModel($phoneNumber);
        }

        return $domains;
    }
}
