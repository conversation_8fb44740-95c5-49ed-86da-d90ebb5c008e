<?php

namespace App\Factories\ChatBot;

use App\Http\Requests\Template\StoreRequest;
use App\Http\Requests\Template\UpdateRequest;
use App\Domains\ChatBot\Template;
use App\Models\Template as TemplateModel;

class TemplateFactory
{

    public ?ComponentFactory $componentFactory;
    public PhoneNumberFactory $phoneNumberFactory;

    public function __construct() {
        $this->phoneNumberFactory = new PhoneNumberFactory();
    }

    private function ensureComponentFactory() : bool {
        if (!isset($this->componentFactory)) {
            $this->componentFactory = new ComponentFactory(
                new ButtonFactory(),
                $this
            );
        }
        return true;
    }

    public function buildFromStoreRequest(StoreRequest $request) : Template {
        return new Template(
            id: null,
            organization_id: $request->organization_id ?? null,
            phone_number_id: $request->phone_number_id ?? null,
            user_id: $request->user_id ?? null,
            client_id: $request->client_id ?? null,
            name: $request->name ?? null,
            category: $request->category ?? null,
            parameter_format: $request->parameter_format ?? null,
            language: $request->language ?? null,
            library_template_name: $request->library_template_name ?? null,
            id_external: $request->id_external ?? null,
            status: $request->status ?? null
        );
    }

    public function buildFromUpdateRequest(UpdateRequest $request) : Template {
        return new Template(
            id: null,
            organization_id: $request->organization_id ?? null,
            phone_number_id: $request->phone_number_id ?? null,
            user_id: $request->user_id ?? null,
            client_id: $request->client_id ?? null,
            name: $request->name ?? null,
            category: $request->category ?? null,
            parameter_format: $request->parameter_format ?? null,
            language: $request->language ?? null,
            library_template_name: $request->library_template_name ?? null,
            id_external: $request->id_external ?? null,
            status: $request->status ?? null
        );
    }

    public function buildFromSaveFullTemplate(
        array $templateData,
        ?int $organization_id,
        ?int $id = null,
        ?int $phone_number_id = null
    ) : Template {
        return new Template(
            id: $id ?? null,
            organization_id: $organization_id ?? null,
            phone_number_id: $phone_number_id ?? null,
            user_id: null,
            client_id: null,
            name: $templateData['name'] ?? null,
            category: $templateData['category'] ?? 'UTILITY',
            parameter_format: null,
            language: is_array($templateData['language'] ?? null)
                ? ($templateData['language']['code'] ?? 'en_US')
                : ($templateData['language'] ?? 'en_US'),
            library_template_name: null,
            id_external: null,
            status: 'PENDING',
            created_at: null,
            updated_at: null,
            is_whatsapp_published: false,
            components: null,
            phone_number: null
        );
    }

    public function buildFromModel(?TemplateModel $template, bool $with_components = true) : ?Template {
        if (!$template){ return null; }

        $isWhatsAppPublished = false;
        if ($template->whatsAppTemplate()->count()){
            $isWhatsAppPublished = true;
        }

        $components = null;
        if($with_components && $this->ensureComponentFactory()){
            $components = [];
            foreach ($template->components as $component){
                $components[] = $this->componentFactory->buildFromModel(
                    $component ?? null, false, true, false
                );
            }
        }

        $phoneNumber = null;
        if ($template->phoneNumber) {
            $phoneNumber = $this->phoneNumberFactory->buildFromModel($template->phoneNumber);
        }

        return new Template(
            id: $template->id ?? null,
            organization_id: $template->organization_id ?? null,
            phone_number_id: $template->phone_number_id ?? null,
            user_id: $template->user_id ?? null,
            client_id: $template->client_id ?? null,
            name: $template->name ?? null,
            category: $template->category ?? null,
            parameter_format: $template->parameter_format ?? null,
            language: $template->language ?? null,
            library_template_name: $template->library_template_name ?? null,
            id_external: $template->id_external ?? null,
            status: $template->status ?? null,
            created_at: $template->created_at ?? null,
            updated_at: $template->updated_at ?? null,
            is_whatsapp_published: $isWhatsAppPublished ?? false,
            components: $components ?? null,
            phone_number: $phoneNumber
        );
    }
}
