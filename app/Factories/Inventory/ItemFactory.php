<?php

namespace App\Factories\Inventory;

use App\Domains\Inventory\Item;
use App\Http\Requests\Item\StoreRequest;
use App\Http\Requests\Item\UpdateRequest;
use App\Models\Sale;
use App\Models\Item as ItemModel;

class ItemFactory
{

    public SaleFactory $saleFactory;
    public ProductFactory $productFactory;

    public function __construct(
        SaleFactory $saleFactory,
        ProductFactory $productFactory,
    ) {
        $this->saleFactory = $saleFactory;
        $this->productFactory = $productFactory;
    }

    public function buildFromStoreRequest(StoreRequest $request) : Item {
        return new Item(
            null,
            $request->organization_id ?? null,
            $request->sale_id ?? null,
            $request->product_id ?? null,
            $request->quantity ?? null,
            $request->value ?? null,
        );
    }

    public function buildFromUpdateRequest(UpdateRequest $request) : Item {
        return new Item(
            null,
            null,
            $request->sale_id ?? null,
            $request->product_id ?? null,
            $request->quantity ?? null,
            $request->value ?? null,
        );
    }

    public function buildFromModel(
        ?ItemModel $item,
        $with_sale = true,
        $with_product = true
    ) : ?Item {
        if (!$item) { return null; }

        $sale = ($with_sale) ? $this->saleFactory->buildFromModel($item->sale ?? null, false, false, false) : null;
        $product = ($with_product) ? $this->productFactory->buildFromModel($item->product ?? null) : null;

        return new Item(
            $item->id ?? null,
            $item->organization_id ?? null,
            $item->sale_id ?? null,
            $item->product_id ?? null,
            $item->quantity ?? null,
            $item->value ?? null,
            $item->created_at ?? null,
            $item->updated_at ?? null,
            $sale ?? null,
            $product ?? null
        );
    }

    public function buildFromSale(Sale $sale) : array {
        $items = [];
        foreach ($sale->items as $item){
            $items[] = new Item(
                $item->id ?? null,
                $item->organization_id ?? null,
                $sale->id ?? null,
                $item->product_id ?? null,
                $item->quantity ?? null,
                $item->value ?? null,
                $item->created_at ?? null,
                $item->updated_at ?? null,
                null,
                $this->productFactory->buildFromModel($item->product ?? null) ?? null,
            );
        }
        return $items;
    }
}
