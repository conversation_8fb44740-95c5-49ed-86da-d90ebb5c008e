<?php

namespace App\Services\OpenAI;

use App\Helpers\DBLog;
use Illuminate\Support\Facades\Http;
use Exception;

class OpenAIService
{
    private string $apiKey;
    private string $baseUrl = 'https://api.openai.com/v1';
    private string $model;

    public function __construct()
    {
        $this->apiKey = config('services.openai.api_key') ?? 'test-key';
        $this->model = config('services.openai.model', 'gpt-4-vision-preview');
    }

    /**
     * Analyze image using ChatGPT Vision and extract medical document data
     *
     * @param string $imagePath
     * @return array
     * @throws Exception
     */
    public function analyzeImage(string $imagePath): array
    {
        DBLog::log(
            "OpenAIService::analyzeImage - Starting analysis",
            "OpenAI",
            request()->user()->organization_id ?? null,
            request()->user()->id ?? null,
            ['image_path' => $imagePath]
        );

        if (!file_exists($imagePath)) {
            throw new Exception("Image file not found at: " . $imagePath);
        }

        try {
            // Convert image to base64
            $imageData = base64_encode(file_get_contents($imagePath));
            $mimeType = mime_content_type($imagePath);

            $prompt = $this->getMedicalDocumentPrompt();

            $payload = [
                'model' => $this->model,
                'messages' => [
                    [
                        'role' => 'user',
                        'content' => [
                            [
                                'type' => 'text',
                                'text' => $prompt
                            ],
                            [
                                'type' => 'image_url',
                                'image_url' => [
                                    'url' => "data:{$mimeType};base64,{$imageData}"
                                ]
                            ]
                        ]
                    ]
                ],
                'max_tokens' => 1500,
                'temperature' => 0.1 // Low temperature for consistent extraction
            ];

            $response = Http::timeout(60)
                ->withHeaders([
                    'Authorization' => "Bearer {$this->apiKey}",
                    'Content-Type' => 'application/json'
                ])
                ->post("{$this->baseUrl}/chat/completions", $payload);

            if (!$response->successful()) {
                throw new Exception("OpenAI API error: " . $response->body());
            }

            $result = $response->json();

            DBLog::log(
                "OpenAI API Response",
                "OpenAI",
                request()->user()->organization_id ?? null,
                request()->user()->id ?? null,
                [
                    'usage' => $result['usage'] ?? null,
                    'model' => $result['model'] ?? null
                ]
            );

            $extractedContent = $result['choices'][0]['message']['content'] ?? '';

            // Try to extract JSON from the response
            $extractedData = $this->parseJsonFromResponse($extractedContent);

            DBLog::log(
                "OpenAIService::analyzeImage - Success",
                "OpenAI",
                request()->user()->organization_id ?? null,
                request()->user()->id ?? null,
                ['extracted_data' => $extractedData]
            );

            return [
                'source' => 'OpenAI ChatGPT',
                'status' => 'success',
                'data' => $extractedData,
                'raw_response' => $extractedContent
            ];

        } catch (Exception $e) {
            DBLog::logError(
                "OpenAIService::analyzeImage - Failed",
                "OpenAI",
                request()->user()->organization_id ?? null,
                request()->user()->id ?? null,
                [
                    'error' => $e->getMessage(),
                    'image_path' => $imagePath,
                    'line' => $e->getLine(),
                    'file' => $e->getFile()
                ]
            );

            throw $e;
        }
    }

    /**
     * Get optimized prompt for medical document analysis
     *
     * @return string
     */
    private function getMedicalDocumentPrompt(): string
    {
        return "Analise esta imagem de documento médico/relatório e extraia as informações organizadas nas seguintes entidades em formato JSON válido.

ESTRUTURA OBRIGATÓRIA:
{
  \"invoice\": {
    \"number\": \"número da nota fiscal ou documento\",
    \"value\": \"valor total em formato decimal (ex: 1500.50)\",
    \"amount_received\": \"valor recebido em formato decimal\",
    \"observation\": \"observações gerais\",
    \"honorary\": \"valor dos honorários em formato decimal\",
    \"origin\": \"origem do documento\",
    \"status\": \"status do procedimento\",
    \"date_payment\": \"data de pagamento no formato YYYY-MM-DD\",
    \"video_fee\": \"taxa de vídeo em formato decimal\",
    \"send_date\": \"data de envio no formato YYYY-MM-DD\",
    \"paid\": \"true/false se foi pago\",
    \"filepath\": null,
    \"nameFile\": null,
    \"client_id\": null,
    \"type_receipt_id\": null,
    \"type_participation_id\": null,
    \"hospital_id\": null,
    \"hospital\": {
      \"name\": \"nome do hospital se identificado\",
      \"desc\": \"descrição ou observações sobre o hospital\"
    },
    \"user_id\": null,
    \"user\": {
      \"name\": \"nome do médico/usuário responsável\",
      \"desc\": \"especialidade ou observações sobre o médico\"
    },
    \"business_id\": null,
    \"business\": {
      \"name\": \"nome da empresa/clínica\",
      \"desc\": \"descrição da empresa\"
    },
    \"agreement_id\": null,
    \"account_id\": null,
    \"account\": {
      \"name\": \"nome da conta\",
      \"desc\": \"descrição da conta\"
    },
    \"reason_glosa_id\": null,
    \"reason_glosa\": {
      \"name\": \"motivo da glosa se houver\",
      \"desc\": \"descrição detalhada da glosa\"
    },
    \"number_note\": \"número da nota\"
  },
  \"client\": {
    \"name\": \"nome completo do paciente\",
    \"email\": \"email do paciente se disponível\",
    \"phone\": \"telefone do paciente\",
    \"cpf\": \"CPF do paciente\",
    \"rg\": \"RG do paciente\",
    \"number_agreements\": \"número do convênio\",
    \"agreement_id\": null,
    \"creator_id\": null,
    \"creator\": {
      \"name\": \"nome de quem criou o registro\",
      \"desc\": \"informações sobre o criador\"
    },
    \"account_id\": null,
    \"account\": {
      \"name\": \"nome da conta do cliente\",
      \"desc\": \"descrição da conta\"
    }
  },
  \"agreement\": {
    \"name\": \"nome do convênio/plano de saúde\",
    \"url\": null,
    \"observation\": \"observações sobre o convênio\"
  },
  \"proceeding\": {
    \"desc\": \"descrição detalhada do procedimento realizado\",
    \"code\": \"código do procedimento se disponível\",
    \"status\": \"status do procedimento\",
    \"size\": \"porte do procedimento\",
    \"operational_cost\": \"custo operacional em formato decimal\",
    \"number_assistants\": \"número de assistentes\",
    \"size_anesthetist\": \"porte do anestesista\"
  },
  \"raw_information\": {
    \"paciente\": \"nome completo do paciente\",
    \"data_nascimento\": \"data no formato DD/MM/AAAA\",
    \"convenio\": \"nome do convênio/plano de saúde\",
    \"data_inicio\": \"data do procedimento no formato DD/MM/AAAA\",
    \"cirurgiao\": \"nome do cirurgião responsável\",
    \"procedimento_descricao\": \"descrição do procedimento\",
    \"diagnostico_pre_operatorio\": \"diagnóstico pré-operatório\"
  },
  \"_token\": \"7438923:jzhdsifhosdihfasdadsf8jxzcoa\"
}

INSTRUÇÕES:
- Se alguma informação não estiver visível, use null
- Para valores monetários, use formato decimal (ex: 1500.50)
- Para datas nas entidades (invoice, client, agreement, proceeding), use formato YYYY-MM-DD
- Para datas em raw_information, use formato DD/MM/AAAA (compatibilidade)
- Para booleanos, use true/false
- Mantenha a estrutura exata do JSON
- Retorne APENAS o JSON válido, sem texto adicional";
    }

    /**
     * Parse JSON from ChatGPT response, handling potential formatting issues
     *
     * @param string $response
     * @return array
     * @throws Exception
     */
    private function parseJsonFromResponse(string $response): array
    {
        // Remove markdown code blocks if present
        $response = preg_replace('/```json\s*/', '', $response);
        $response = preg_replace('/```\s*$/', '', $response);

        // Trim whitespace
        $response = trim($response);

        // Try to decode JSON
        $decoded = json_decode($response, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            // Try to find JSON within the response
            if (preg_match('/\{.*\}/s', $response, $matches)) {
                $decoded = json_decode($matches[0], true);
            }

            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new Exception('Failed to parse JSON from OpenAI response: ' . json_last_error_msg());
            }
        }

        // Validate required structure
        if (!is_array($decoded)) {
            throw new Exception('OpenAI response is not a valid array');
        }

        return $decoded;
    }
}
