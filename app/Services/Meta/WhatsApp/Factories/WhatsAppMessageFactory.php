<?php

namespace App\Services\Meta\WhatsApp\Factories;

use App\Domains\ChatBot\Message;
use App\Factories\ChatBot\MessageFactory;
use App\Services\Meta\WhatsApp\Domains\WhatsAppMessage;
use App\Services\Meta\WhatsApp\Models\WhatsAppMessage as WhatsAppMessageModel;

class WhatsAppMessageFactory
{
    private MessageFactory $messageFactory;

    public function __construct(MessageFactory $messageFactory)
    {
        $this->messageFactory = $messageFactory;
    }

    public function buildFromModel(?WhatsAppMessageModel $whatsappMessage): ?WhatsAppMessage
    {
        if (!$whatsappMessage) {
            return null;
        }

        $message = null;
        if ($whatsappMessage->message) {
            $message = $this->messageFactory->buildFromModel($whatsappMessage->message, true);
        }

        return new WhatsAppMessage(
            id: $whatsappMessage->id ?? null,
            message_id: $whatsappMessage->message_id ?? null,
            message: $message,
            whatsapp_message_id: $whatsappMessage->whatsapp_message_id ?? null,
            message_status: $whatsappMessage->message_status ?? null,
            wa_id: $whatsappMessage->wa_id ?? null,
            input_phone: $whatsappMessage->input_phone ?? null,
            messaging_product: $whatsappMessage->messaging_product ?? 'whatsapp',
            json: is_array($whatsappMessage->json) ? json_encode($whatsappMessage->json) : $whatsappMessage->json,
            webhook_entries: null,
            last_status_check: $whatsappMessage->last_status_check ?? null,
            status_check_count: $whatsappMessage->status_check_count ?? 0,
            delivery_confirmed_at: $whatsappMessage->delivery_confirmed_at ?? null,
            needs_status_check: $whatsappMessage->needs_status_check ?? false,
            created_at: $whatsappMessage->created_at ?? null,
            updated_at: $whatsappMessage->updated_at ?? null
        );
    }

    /**
     * Build WhatsAppMessage domain from Meta API response
     *
     * @param int $messageId
     * @param array $whatsappResponse
     * @return WhatsAppMessage
     */
    public function buildFromMetaApiResponse(int $messageId, array $whatsappResponse): WhatsAppMessage
    {
        // Extract data from WhatsApp response
        $whatsappMessageId = $whatsappResponse['messages'][0]['id'] ?? null;
        $messageStatus = $whatsappResponse['messages'][0]['message_status'] ?? null;
        $waId = $whatsappResponse['contacts'][0]['wa_id'] ?? null;
        $inputPhone = $whatsappResponse['contacts'][0]['input'] ?? null;
        $messagingProduct = $whatsappResponse['messaging_product'] ?? 'whatsapp';

        // Set default message_status to 'accepted' only if we have a message ID
        if ($whatsappMessageId && !$messageStatus) {
            $messageStatus = 'accepted';
        }

        return new WhatsAppMessage(
            id: null, // id - will be set after saving
            message_id: $messageId,
            message: null, // message - will be loaded if needed
            whatsapp_message_id: $whatsappMessageId,
            message_status: $messageStatus,
            wa_id: $waId,
            input_phone: $inputPhone,
            messaging_product: $messagingProduct,
            json: json_encode($whatsappResponse),
            webhook_entries: null,
            last_status_check: null,
            status_check_count: 0,
            delivery_confirmed_at: null,
            needs_status_check: false,
            created_at: null, // created_at - will be set by database
            updated_at: null  // updated_at - will be set by database
        );
    }
}
