<?php

namespace App\Services\Meta\WhatsApp\Repositories;

use App\Services\Meta\WhatsApp\Domains\WhatsAppMessage;
use App\Services\Meta\WhatsApp\Factories\WhatsAppMessageFactory;
use App\Services\Meta\WhatsApp\Models\WhatsAppMessage as WhatsAppMessageModel;

class WhatsAppMessageRepository
{
    private WhatsAppMessageFactory $whatsAppMessageFactory;

    public function __construct(WhatsAppMessageFactory $whatsAppMessageFactory)
    {
        $this->whatsAppMessageFactory = $whatsAppMessageFactory;
    }

    public function fetchByMessageId(int $messageId): ?WhatsAppMessage
    {
        $model = WhatsAppMessageModel::where('message_id', $messageId)
            ->with('message')
            ->first();

        return $this->whatsAppMessageFactory->buildFromModel($model);
    }

    public function fetchByWhatsAppMessageId(string $whatsappMessageId): ?WhatsAppMessage
    {
        $model = WhatsAppMessageModel::where('whatsapp_message_id', $whatsappMessageId)
            ->with('message')
            ->first();

        return $this->whatsAppMessageFactory->buildFromModel($model);
    }

    public function fetchById(int $id): ?WhatsAppMessage
    {
        $model = WhatsAppMessageModel::with('message')->find($id);

        return $this->whatsAppMessageFactory->buildFromModel($model);
    }

    public function store(WhatsAppMessage $whatsAppMessage): WhatsAppMessage
    {
        $savedWhatsAppMessage = WhatsAppMessageModel::create($whatsAppMessage->toStoreArray());

        $whatsAppMessage->id = $savedWhatsAppMessage->id;

        return $whatsAppMessage;
    }

    public function update(WhatsAppMessage $whatsAppMessage): WhatsAppMessage
    {
        WhatsAppMessageModel::where('id', $whatsAppMessage->id)
            ->update($whatsAppMessage->toUpdateArray());

        return $whatsAppMessage;
    }

    public function save(WhatsAppMessage $whatsAppMessage): WhatsAppMessage
    {
        if ($whatsAppMessage->id) {
            return $this->update($whatsAppMessage);
        }
        return $this->store($whatsAppMessage);
    }

    public function delete(WhatsAppMessage $whatsAppMessage): bool
    {
        return WhatsAppMessageModel::find($whatsAppMessage->id)->delete();
    }

    public function fetchByExternalWamId(string $externalWamId): ?WhatsAppMessage
    {
        $model = WhatsAppMessageModel::where('whatsapp_message_id', $externalWamId)
            ->with('message')
            ->first();

        return $this->whatsAppMessageFactory->buildFromModel($model);
    }
}
