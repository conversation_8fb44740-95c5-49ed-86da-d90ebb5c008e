<?php

namespace App\Services\Meta\WhatsApp\UseCases\Webhook;

use App\Domains\WhatsApp\ChangeValue;

class ValidateWebhookPayload
{
    /**
     * Validate webhook payload structure from Meta
     *
     * @param array $webhookData
     * @return bool
     */
    public function perform(array $webhookData): bool
    {
        // Check if required top-level fields exist
        if (!isset($webhookData['object'])) {
            return false;
        }

        // Validate object type
        if ($webhookData['object'] !== 'whatsapp_business_account') {
            return false;
        }

        // Check if entry exists and is array
        if (!isset($webhookData['entry']) || !is_array($webhookData['entry'])) {
            return false;
        }

        // Validate entry structure
        foreach ($webhookData['entry'] as $entry) {
            if (!is_array($entry)) {
                return false;
            }

            // Each entry should have changes
            if (!isset($entry['changes']) || !is_array($entry['changes'])) {
                return false;
            }

            // Validate changes structure
            foreach ($entry['changes'] as $change) {
                if (!is_array($change)) {
                    return false;
                }

                // Each change should have field and value
                if (!isset($change['field']) || !isset($change['value'])) {
                    return false;
                }

                // Field should be 'messages' for message webhooks
                if ($change['field'] === 'messages') {
                    $changeValue = new ChangeValue($change['value']);
                    if (!$changeValue->validateMessageChange()) {
                        return false;
                    }
                }
            }
        }

        return true;
    }


}
