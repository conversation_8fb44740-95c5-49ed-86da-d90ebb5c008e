<?php

namespace App\Services\Meta\WhatsApp\UseCases\WhatsAppMessage;

use App\Domains\WhatsApp\ChangeValue;
use App\Services\Meta\WhatsApp\Domains\WhatsAppMessage;
use App\Services\Meta\WhatsApp\Repositories\WhatsAppMessageRepository;

class FetchByWAMID
{
    private WhatsAppMessageRepository $whatsAppMessageRepository;

    public function __construct(WhatsAppMessageRepository $whatsAppMessageRepository)
    {
        $this->whatsAppMessageRepository = $whatsAppMessageRepository;
    }

    /**
     * Fetch WhatsApp message by WAM ID from ChangeValue
     *
     * @param ChangeValue $changeValue
     * @return WhatsAppMessage|null
     */
    public function perform(ChangeValue $changeValue): ?WhatsAppMessage
    {
        $wamId = $changeValue->getPrimaryWamId();

        if (!$wamId) {
            return null;
        }

        return $this->whatsAppMessageRepository->fetchByExternalWamId($wamId);
    }
}
