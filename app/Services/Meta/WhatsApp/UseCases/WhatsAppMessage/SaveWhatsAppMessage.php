<?php

namespace App\Services\Meta\WhatsApp\UseCases\WhatsAppMessage;

use App\Services\Meta\WhatsApp\Domains\WhatsAppMessage;
use App\Services\Meta\WhatsApp\Factories\WhatsAppMessageFactory;
use App\Services\Meta\WhatsApp\Repositories\WhatsAppMessageRepository;
use Illuminate\Support\Facades\DB;

class SaveWhatsAppMessage
{
    private WhatsAppMessageRepository $whatsAppMessageRepository;
    private WhatsAppMessageFactory $whatsAppMessageFactory;

    public function __construct(
        WhatsAppMessageRepository $whatsAppMessageRepository,
        WhatsAppMessageFactory $whatsAppMessageFactory
    ) {
        $this->whatsAppMessageRepository = $whatsAppMessageRepository;
        $this->whatsAppMessageFactory = $whatsAppMessageFactory;
    }

    /**
     * Save WhatsApp message tracking data after message is sent
     *
     * @param int $messageId
     * @param array $whatsappResponse
     * @return WhatsAppMessage
     */
    public function perform(int $messageId, array $whatsappResponse): WhatsAppMessage
    {
        DB::beginTransaction();

        try {
            // Build domain from Meta API response
            $whatsAppMessage = $this->whatsAppMessageFactory->buildFromMetaApiResponse(
                $messageId,
                $whatsappResponse
            );

            // Save to database
            $savedWhatsAppMessage = $this->whatsAppMessageRepository->store($whatsAppMessage);

            DB::commit();

            return $savedWhatsAppMessage;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }
}
