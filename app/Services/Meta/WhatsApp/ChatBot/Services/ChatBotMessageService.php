<?php

namespace App\Services\Meta\WhatsApp\ChatBot\Services;

use App\Domains\ChatBot\Message;
use App\Domains\ChatBot\PhoneNumber;
use App\Domains\Inventory\Client;
use App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppConversation;
use App\Services\VariableSubstitution\VariableSubstitutionService;
use App\Services\Meta\WhatsApp\WhatsAppService;
use Exception;

class ChatBotMessageService extends WhatsAppService
{
    protected VariableSubstitutionService $substitutionService;

    public function __construct(?PhoneNumber $phoneNumber = null)
    {
        parent::__construct($phoneNumber);
        $this->endpoint = "messages";
        $this->substitutionService = new VariableSubstitutionService();
    }

    /**
     * Send a simple text message with variable substitution
     *
     * @param string $messageText
     * @param Client $client
     * @param WhatsAppConversation|null $conversation
     * @param array $additionalModels
     * @return array
     * @throws Exception
     */
    public function sendTextMessage(
        string $messageText, 
        Client $client, 
        ?WhatsAppConversation $conversation = null,
        array $additionalModels = []
    ): array {
        // Prepare available models for substitution
        $availableModels = $this->prepareModelsForSubstitution($client, $conversation, $additionalModels);
        
        // Apply variable substitution
        $processedMessage = $this->substitutionService->substitute($messageText, $availableModels);
        
        // Create WhatsApp payload
        $payload = [
            'messaging_product' => 'whatsapp',
            'to' => $client->phone,
            'type' => 'text',
            'text' => [
                'body' => $processedMessage
            ]
        ];

        // Send message
        $response = $this->post($payload);
        $responseData = json_decode((string) $response->getBody(), true);

        return $responseData;
    }

    /**
     * Send an interactive message with buttons
     *
     * @param string $messageText
     * @param array $buttons
     * @param Client $client
     * @param WhatsAppConversation|null $conversation
     * @param array $additionalModels
     * @return array
     * @throws Exception
     */
    public function sendInteractiveMessage(
        string $messageText,
        array $buttons,
        Client $client,
        ?WhatsAppConversation $conversation = null,
        array $additionalModels = []
    ): array {
        // Prepare available models for substitution
        $availableModels = $this->prepareModelsForSubstitution($client, $conversation, $additionalModels);
        
        // Apply variable substitution to message text
        $processedMessage = $this->substitutionService->substitute($messageText, $availableModels);
        
        // Process buttons with variable substitution
        $processedButtons = $this->processButtonsWithSubstitution($buttons, $availableModels);
        
        // Create WhatsApp interactive payload
        $payload = [
            'messaging_product' => 'whatsapp',
            'to' => $client->phone,
            'type' => 'interactive',
            'interactive' => [
                'type' => 'button',
                'body' => [
                    'text' => $processedMessage
                ],
                'action' => [
                    'buttons' => $processedButtons
                ]
            ]
        ];

        // Send message
        $response = $this->post($payload);
        $responseData = json_decode((string) $response->getBody(), true);

        return $responseData;
    }

    /**
     * Send a template message with variable substitution
     *
     * @param Message $message
     * @return array
     * @throws Exception
     */
    public function sendTemplateMessage(Message $message): array
    {
        if (!$message->template) {
            throw new Exception("Template is required for template message");
        }

        if (!$message->client || !$message->client->phone) {
            throw new Exception("Client with phone number is required");
        }

        // Use the improved Message domain method
        $payload = $message->toWhatsAppTemplatePayload();

        // Send message
        $response = $this->post($payload);
        $responseData = json_decode((string) $response->getBody(), true);

        return $responseData;
    }

    /**
     * Prepare models for variable substitution
     *
     * @param Client $client
     * @param WhatsAppConversation|null $conversation
     * @param array $additionalModels
     * @return array
     */
    private function prepareModelsForSubstitution(
        Client $client,
        ?WhatsAppConversation $conversation = null,
        array $additionalModels = []
    ): array {
        $models = [
            'client' => $client,
        ];

        if ($conversation) {
            $models['conversation'] = $conversation;
            
            if ($conversation->flow) {
                $models['flow'] = $conversation->flow;
            }
            
            if ($conversation->phone_number) {
                $models['phone_number'] = $conversation->phone_number;
            }
            
            if ($conversation->current_step) {
                $models['step'] = $conversation->current_step;
            }
        }

        // Add any additional models
        return array_merge($models, $additionalModels);
    }

    /**
     * Process buttons with variable substitution
     *
     * @param array $buttons
     * @param array $availableModels
     * @return array
     */
    private function processButtonsWithSubstitution(array $buttons, array $availableModels): array
    {
        $processedButtons = [];
        
        foreach ($buttons as $index => $button) {
            $buttonText = $button['text'] ?? "Button " . ($index + 1);
            $buttonId = $button['id'] ?? "button_" . $index;
            
            // Apply variable substitution to button text
            $processedText = $this->substitutionService->substitute($buttonText, $availableModels);
            
            $processedButtons[] = [
                'type' => 'reply',
                'reply' => [
                    'id' => $buttonId,
                    'title' => $processedText
                ]
            ];
        }
        
        return $processedButtons;
    }

    /**
     * Validate message variables before sending
     *
     * @param string $messageText
     * @param array $availableModels
     * @return array
     */
    public function validateMessageVariables(string $messageText, array $availableModels): array
    {
        return $this->substitutionService->validateVariables($messageText, $availableModels);
    }

    /**
     * Get all variables found in message text
     *
     * @param string $messageText
     * @return array
     */
    public function getMessageVariables(string $messageText): array
    {
        return $this->substitutionService->getVariablesFromText($messageText);
    }
}
