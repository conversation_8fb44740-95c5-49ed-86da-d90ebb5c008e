<?php

namespace App\Services\Meta\WhatsApp\ChatBot\UseCases;

use App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppConversation;
use App\Services\Meta\WhatsApp\ChatBot\Repositories\WhatsAppConversationRepository;
use App\Services\Meta\WhatsApp\ChatBot\Services\ChatBotMessageService;
use App\Domains\ChatBot\Message;
use App\Domains\ChatBot\PhoneNumber;
use Exception;

class SendWhatsAppResponse
{
    protected WhatsAppConversationRepository $conversationRepository;

    public function __construct(
        WhatsAppConversationRepository $conversationRepository
    ) {
        $this->conversationRepository = $conversationRepository;
    }

    /**
     * Send WhatsApp response based on step result
     *
     * @param array $stepResult
     * @param WhatsAppConversation $conversation
     * @return array
     * @throws Exception
     */
    public function perform(array $stepResult, WhatsAppConversation $conversation): array
    {
        try {
            // Check if conversation should be finished
            if ($stepResult['finish_conversation'] ?? false) {
                $this->finishConversation($conversation, $stepResult);
            }

            // Update conversation state if needed
            if ($stepResult['move_to_next'] ?? false) {
                $this->moveConversationToNextStep($conversation, $stepResult);
            }

            // Send response message if needed
            $response = null;
            if ($this->shouldSendMessage($stepResult)) {
                $response = $this->sendMessage($stepResult, $conversation);
            }

            return [
                'sent' => $response !== null,
                'response' => $response,
                'conversation_updated' => $stepResult['move_to_next'] ?? false,
                'conversation_finished' => $stepResult['finish_conversation'] ?? false,
                'step_result' => $stepResult,
            ];

        } catch (Exception $e) {
            \Log::error('Error sending WhatsApp response', [
                'error' => $e->getMessage(),
                'step_result' => $stepResult,
                'conversation_id' => $conversation->id,
            ]);

            throw $e;
        }
    }

    /**
     * Move conversation to next step
     */
    protected function moveConversationToNextStep(WhatsAppConversation $conversation, array $stepResult): void
    {
        $nextStepId = $stepResult['next_step'] ?? null;

        if ($nextStepId) {
            $conversation->current_step_id = is_string($nextStepId) ? (int) $nextStepId : $nextStepId;
            // TODO: Load next step object
            // $conversation->current_step = $this->stepRepository->findById($nextStepId);
        } else {
            // No next step, mark conversation as finished
            $conversation->is_finished = true;
            $conversation->current_step_id = null;
            $conversation->current_step = null;
        }

        $this->conversationRepository->save($conversation);
    }

    /**
     * Finish conversation due to error or completion
     */
    protected function finishConversation(WhatsAppConversation $conversation, array $stepResult): void
    {
        $conversation->is_finished = true;
        $conversation->current_step_id = null;
        $conversation->current_step = null;

        \Log::info('Conversation finished', [
            'conversation_id' => $conversation->id,
            'reason' => $stepResult['action'] ?? 'unknown',
            'error' => $stepResult['error'] ?? null
        ]);

        $this->conversationRepository->save($conversation);
    }

    /**
     * Check if we should send a message for this step result
     */
    protected function shouldSendMessage(array $stepResult): bool
    {
        $action = $stepResult['action'] ?? '';

        // Send message for these actions
        $sendActions = [
            'send_message',
            'show_options',
            'request_input',
            'input_invalid',
            'input_processed',
            'input_error_finish',
            'default_processing',
        ];

        return in_array($action, $sendActions) && !empty($stepResult['message']);
    }

    /**
     * Send message to WhatsApp
     */
    protected function sendMessage(array $stepResult, WhatsAppConversation $conversation): array
    {
        $messageText = $stepResult['message'] ?? '';

        if (empty($messageText)) {
            throw new Exception('No message content to send');
        }

        // Get phone number for message service
        $phoneNumber = $conversation->phone_number;
        if (!$phoneNumber) {
            throw new Exception('No phone number found for conversation');
        }

        // Create ChatBot message service
        $messageService = new ChatBotMessageService($phoneNumber);

        // Get client
        if (!$conversation->client_id) {
            throw new Exception('No client found for conversation');
        }

        // Load client (you might want to inject a client repository)
        $client = app()->make(\App\Repositories\ClientRepository::class)->fetchById($conversation->client_id);
        if (!$client) {
            throw new Exception('Client not found');
        }

        // Send message based on step type
        return $this->sendMessageByType($stepResult, $messageService, $client, $conversation);
    }

    /**
     * Send message based on step type
     */
    protected function sendMessageByType(
        array $stepResult,
        ChatBotMessageService $messageService,
        $client,
        WhatsAppConversation $conversation
    ): array {
        $messageText = $stepResult['message'] ?? '';
        $stepType = $stepResult['type'] ?? 'text';

        try {
            switch ($stepType) {
                case 'interactive':
                    return $this->sendInteractiveMessage($stepResult, $messageService, $client, $conversation);

                case 'input':
                case 'message':
                case 'default':
                default:
                    return $this->sendTextMessage($messageText, $messageService, $client, $conversation);
            }
        } catch (Exception $e) {
            \Log::error('Error sending WhatsApp message', [
                'error' => $e->getMessage(),
                'step_result' => $stepResult,
                'conversation_id' => $conversation->id,
            ]);

            // Return mock response for now
            return [
                'message_id' => 'error_' . uniqid(),
                'status' => 'error',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Send text message with variable substitution
     */
    protected function sendTextMessage(
        string $messageText,
        ChatBotMessageService $messageService,
        $client,
        WhatsAppConversation $conversation
    ): array {
        return $messageService->sendTextMessage($messageText, $client, $conversation);
    }

    /**
     * Send interactive message with buttons
     */
    protected function sendInteractiveMessage(
        array $stepResult,
        ChatBotMessageService $messageService,
        $client,
        WhatsAppConversation $conversation
    ): array {
        $messageText = $stepResult['message'] ?? '';
        $options = $stepResult['options'] ?? [];

        // Convert options to button format
        $buttons = [];
        foreach ($options as $index => $option) {
            $buttons[] = [
                'id' => 'option_' . $index,
                'text' => $option
            ];
        }

        return $messageService->sendInteractiveMessage($messageText, $buttons, $client, $conversation);
    }
}
