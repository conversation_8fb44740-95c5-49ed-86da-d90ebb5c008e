<?php

namespace App\Services\Meta\WhatsApp\ChatBot\Repositories;

use App\Domains\ChatBot\Step;
use App\Factories\ChatBot\StepFactory;
use App\Models\Step as StepModel;

class StepRepository
{
    protected StepFactory $stepFactory;

    public function __construct(StepFactory $stepFactory)
    {
        $this->stepFactory = $stepFactory;
    }

    /**
     * Find step by identifier within a flow
     *
     * @param string $stepIdentifier
     * @param int $flowId
     * @return Step|null
     */
    public function findByIdentifierInFlow(string $stepIdentifier, int $flowId): ?Step
    {
        $model = StepModel::where('step', $stepIdentifier)
            ->where('flow_id', $flowId)
            ->with(['flow', 'components.buttons'])
            ->first();

        return $model ? $this->stepFactory->buildFromModel($model) : null;
    }

    /**
     * Find step by ID
     *
     * @param int $stepId
     * @return Step|null
     */
    public function findById(int $stepId): ?Step
    {
        $model = StepModel::with(['flow', 'components.buttons'])
            ->find($stepId);

        return $model ? $this->stepFactory->buildFromModel($model) : null;
    }

    /**
     * Get all steps in a flow
     *
     * @param int $flowId
     * @return array
     */
    public function getStepsInFlow(int $flowId): array
    {
        $models = StepModel::where('flow_id', $flowId)
            ->with(['flow', 'components.buttons'])
            ->orderBy('position')
            ->get();

        return $models->map(function ($model) {
            return $this->stepFactory->buildFromModel($model);
        })->filter()->toArray();
    }

    /**
     * Get initial step for flow
     *
     * @param int $flowId
     * @return Step|null
     */
    public function getInitialStepForFlow(int $flowId): ?Step
    {
        $model = StepModel::where('flow_id', $flowId)
            ->where('is_initial_step', true)
            ->with(['flow', 'components.buttons'])
            ->first();

        if (!$model) {
            // Fallback to first step by position
            $model = StepModel::where('flow_id', $flowId)
                ->orderBy('position')
                ->with(['flow', 'components.buttons'])
                ->first();
        }

        return $model ? $this->stepFactory->buildFromModel($model) : null;
    }

    /**
     * Check if step identifier exists in flow
     *
     * @param string $stepIdentifier
     * @param int $flowId
     * @return bool
     */
    public function stepIdentifierExistsInFlow(string $stepIdentifier, int $flowId): bool
    {
        return StepModel::where('step', $stepIdentifier)
            ->where('flow_id', $flowId)
            ->exists();
    }

    /**
     * Get all step identifiers in a flow
     *
     * @param int $flowId
     * @return array
     */
    public function getStepIdentifiersInFlow(int $flowId): array
    {
        return StepModel::where('flow_id', $flowId)
            ->pluck('step')
            ->filter()
            ->toArray();
    }

    /**
     * Validate conditional navigation in flow
     *
     * @param int $flowId
     * @return array
     */
    public function validateConditionalNavigation(int $flowId): array
    {
        $errors = [];
        $stepIdentifiers = $this->getStepIdentifiersInFlow($flowId);
        
        $steps = StepModel::where('flow_id', $flowId)
            ->with(['components.buttons'])
            ->get();

        foreach ($steps as $step) {
            foreach ($step->components as $component) {
                foreach ($component->buttons as $button) {
                    if ($button->internal_type === 'condition') {
                        $callbackData = json_decode($button->callback_data, true);
                        $targetStep = $callbackData['target_step'] ?? $button->callback_data;
                        
                        if (!in_array($targetStep, $stepIdentifiers)) {
                            $errors[] = "Button '{$button->text}' in step '{$step->step}' references non-existent step '{$targetStep}'";
                        }
                    }
                }
            }
        }

        return $errors;
    }
}
