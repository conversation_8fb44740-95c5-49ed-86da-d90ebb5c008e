<?php

namespace App\Services\ASAAS\UseCases\Resources\AsaasOrganizationCustomer;

use App\Services\ASAAS\Repositories\AsaasOrganizationCustomerRepository;
use Exception;

class Delete
{
    private AsaasOrganizationCustomerRepository $asaasOrganizationCustomerRepository;

    public function __construct(AsaasOrganizationCustomerRepository $asaasOrganizationCustomerRepository)
    {
        $this->asaasOrganizationCustomerRepository = $asaasOrganizationCustomerRepository;
    }

    /**
     * @param int $id
     * @return bool
     * @throws Exception
     */
    public function perform(int $id): bool
    {
        $organization_id = request()->user()->organization_id;

        $asaasOrganizationCustomer = $this->asaasOrganizationCustomerRepository->findById($id);

        if (!$asaasOrganizationCustomer) {
            throw new Exception(
                "ASAAS Organization Customer not found.",
                404
            );
        }

        if ($asaasOrganizationCustomer->organization_id !== $organization_id) {
            throw new Exception(
                "This ASAAS Organization Customer doesn't belong to this organization.",
                403
            );
        }

        return $this->asaasOrganizationCustomerRepository->delete($id);
    }
}
