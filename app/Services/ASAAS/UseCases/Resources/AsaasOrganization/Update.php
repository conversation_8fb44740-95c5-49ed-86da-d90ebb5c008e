<?php

namespace App\Services\ASAAS\UseCases\Resources\AsaasOrganization;

use App\Services\ASAAS\Domains\AsaasOrganization;
use App\Services\ASAAS\Factories\AsaasOrganizationFactory;
use App\Http\Requests\ASAAS\AsaasOrganization\UpdateAsaasOrganizationRequest;
use App\Services\ASAAS\Repositories\AsaasOrganizationRepository;
use Illuminate\Support\Facades\DB;

class Update
{
    private AsaasOrganizationRepository $asaasOrganizationRepository;
    private AsaasOrganizationFactory $asaasOrganizationFactory;

    public function __construct(AsaasOrganizationRepository $asaasOrganizationRepository, AsaasOrganizationFactory $asaasOrganizationFactory)
    {
        $this->asaasOrganizationRepository = $asaasOrganizationRepository;
        $this->asaasOrganizationFactory = $asaasOrganizationFactory;
    }

    /**
     * @param UpdateAsaasOrganizationRequest $request
     * @param int $id
     * @return AsaasOrganization
     */
    public function perform(UpdateAsaasOrganizationRequest $request, int $id): AsaasOrganization
    {
        DB::beginTransaction();

        $domain = $this->asaasOrganizationFactory->buildFromUpdateRequest($request);
        $domain->id = $id;

        $asaasOrganization = $this->asaasOrganizationRepository->update($domain);

        DB::commit();

        return $asaasOrganization;
    }
}
