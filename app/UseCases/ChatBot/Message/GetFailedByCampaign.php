<?php

namespace App\UseCases\ChatBot\Message;

use App\Repositories\CampaignRepository;
use App\Repositories\MessageRepository;
use App\Helpers\DBLog;
use Throwable;

class GetFailedByCampaign
{
    private CampaignRepository $campaignRepository;
    private MessageRepository $messageRepository;

    public function __construct(
        CampaignRepository $campaignRepository,
        MessageRepository $messageRepository
    ) {
        $this->campaignRepository = $campaignRepository;
        $this->messageRepository = $messageRepository;
    }

    /**
     * Get messages by campaign
     * @param int $campaign_id
     * @param int $organization_id
     * @return array
     * @throws Throwable
     */
    public function perform(
        int $campaign_id,
        int $organization_id
    ): array {
        try {
            $campaign = $this->campaignRepository->fetchById($campaign_id);
            if ($campaign->organization_id !== $organization_id) {
                throw new \InvalidArgumentException("Campaign not found or access denied");
            }

            return $this->messageRepository->fetchFailedByCampaignId($campaign_id);

        } catch (Throwable $e) {
            DBLog::logError(
                $e->getMessage(),
                "Message::GetByCampaign",
                $organization_id,
                null,
                ['campaign_id' => $campaign_id]
            );
            throw $e;
        }
    }
}
