<?php

namespace App\UseCases\ChatBot\Conversation;

use App\Domains\ChatBot\Conversation;
use App\Repositories\ConversationRepository;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\Log;

class Close
{
    private ConversationRepository $conversationRepository;

    public function __construct(ConversationRepository $conversationRepository)
    {
        $this->conversationRepository = $conversationRepository;
    }

    /**
     * Close a conversation
     *
     * @param Conversation $conversation
     * @param Carbon $lastActivityTime
     * @param int $inactivityMinutes
     * @return Conversation
     * @throws Exception
     */
    public function perform(Conversation $conversation, Carbon $lastActivityTime, int $inactivityMinutes): Conversation
    {
        try {
            $conversation->markAsClosedByInactivity($lastActivityTime, $inactivityMinutes);

            return $this->conversationRepository->update($conversation, $conversation->organization_id);
        } catch (Exception $e) {
            Log::error('Failed to close conversation', [
                'conversation_id' => $conversation->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw new Exception("Failed to close conversation {$conversation->id}: " . $e->getMessage());
        }
    }
}
