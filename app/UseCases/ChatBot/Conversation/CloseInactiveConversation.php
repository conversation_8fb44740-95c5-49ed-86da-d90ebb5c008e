<?php

namespace App\UseCases\ChatBot\Conversation;

use App\Domains\ChatBot\Conversation;
use App\Repositories\ConversationRepository;
use App\UseCases\ChatBot\Conversation\Close;
use App\UseCases\ChatBot\Conversation\GetLastActivityTime;
use Carbon\Carbon;
use Exception;

class CloseInactiveConversation
{
    private ConversationRepository $conversationRepository;
    private Close $closeConversationUseCase;
    private GetLastActivityTime $getLastActivityTimeUseCase;

    public function __construct(
        ConversationRepository $conversationRepository,
        Close $closeConversationUseCase,
        GetLastActivityTime $getLastActivityTimeUseCase
    ) {
        $this->conversationRepository = $conversationRepository;
        $this->closeConversationUseCase = $closeConversationUseCase;
        $this->getLastActivityTimeUseCase = $getLastActivityTimeUseCase;
    }

    /**
     * Close conversation if it's inactive based on flow's inactivity_minutes
     *
     * @param Conversation $conversation
     * @return bool Returns true if conversation was closed, false if still active
     * @throws Exception
     */
    public function perform(Conversation $conversation): bool
    {
        if ($conversation->is_finished) {
            return false;
        }

        if (!$conversation->flow) {
            return false;
        }

        $inactivityMinutes = $conversation->flow->getTimeoutMinutes();

        // Get the last activity time using dedicated UseCase
        $lastActivityTime = $this->getLastActivityTimeUseCase->perform($conversation);

        if ($conversation->isConversationInactive($lastActivityTime, $inactivityMinutes)) {
            $this->closeConversationUseCase->perform($conversation, $lastActivityTime, $inactivityMinutes);
            return true;
        }

        return false;
    }


}
