<?php

namespace App\UseCases\ChatBot\Conversation;

use App\Domains\ChatBot\Conversation;
use Exception;

class CloseInactiveConversation
{
    private Close $closeConversationUseCase;
    private GetLastActivityTime $getLastActivityTimeUseCase;

    public function __construct(
        Close $closeConversationUseCase,
        GetLastActivityTime $getLastActivityTimeUseCase
    ) {
        $this->closeConversationUseCase = $closeConversationUseCase;
        $this->getLastActivityTimeUseCase = $getLastActivityTimeUseCase;
    }

    /**
     * Close conversation if it's inactive based on flow's inactivity_minutes
     *
     * @param Conversation $conversation
     * @return bool Returns true if the conversation was closed, false if still active
     * @throws Exception
     */
    public function perform(Conversation $conversation): bool
    {
        if ($conversation->is_finished) {
            return false;
        }

        if (!$conversation->flow) {
            return false;
        }

        $inactivityMinutes = $conversation->flow->getTimeoutMinutes();

        $lastActivityTime = $this->getLastActivityTimeUseCase->perform($conversation);

        if ($conversation->isConversationInactive($lastActivityTime, $inactivityMinutes)) {
            $this->closeConversationUseCase->perform($conversation, $lastActivityTime, $inactivityMinutes);
            return true;
        }

        return false;
    }
}
