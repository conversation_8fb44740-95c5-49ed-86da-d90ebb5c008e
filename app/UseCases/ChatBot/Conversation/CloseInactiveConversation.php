<?php

namespace App\UseCases\ChatBot\Conversation;

use App\Domains\ChatBot\Conversation;
use App\Repositories\ConversationRepository;
use App\Repositories\InteractionRepository;
use Carbon\Carbon;
use Exception;

class CloseInactiveConversation
{
    private ConversationRepository $conversationRepository;
    private InteractionRepository $interactionRepository;

    public function __construct(
        ConversationRepository $conversationRepository,
        InteractionRepository $interactionRepository
    ) {
        $this->conversationRepository = $conversationRepository;
        $this->interactionRepository = $interactionRepository;
    }

    /**
     * Close conversation if it's inactive based on flow's inactivity_minutes
     *
     * @param Conversation $conversation
     * @return bool Returns true if conversation was closed, false if still active
     * @throws Exception
     */
    public function perform(Conversation $conversation): bool
    {
        // Skip if conversation is already finished
        if ($conversation->is_finished) {
            return false;
        }

        // Skip if conversation doesn't have a flow
        if (!$conversation->flow) {
            return false;
        }

        // Get inactivity timeout from flow (default to 60 minutes if not set)
        $inactivityMinutes = $conversation->flow->getTimeoutMinutes();

        // Get the last interaction for this conversation
        $lastInteraction = $this->getLastInteractionForConversation($conversation->id);

        // Determine the last activity time
        $lastActivityTime = $this->getLastActivityTime($conversation, $lastInteraction);

        // Check if conversation is inactive
        if ($this->isConversationInactive($lastActivityTime, $inactivityMinutes)) {
            return $this->closeConversation($conversation, $lastActivityTime, $inactivityMinutes);
        }

        return false;
    }

    /**
     * Get the last interaction for a conversation
     *
     * @param int $conversationId
     * @return \App\Domains\ChatBot\Interaction|null
     */
    private function getLastInteractionForConversation(int $conversationId): ?\App\Domains\ChatBot\Interaction
    {
        try {
            // Use raw query to get the latest interaction by created_at
            $latestInteraction = \App\Models\Interaction::where('conversation_id', $conversationId)
                ->orderBy('created_at', 'desc')
                ->first();

            if (!$latestInteraction) {
                return null;
            }

            // Build domain object from model
            $interactionFactory = app()->make(\App\Factories\ChatBot\InteractionFactory::class);
            return $interactionFactory->buildFromModel($latestInteraction);
        } catch (Exception $e) {
            \Log::warning('Failed to get last interaction for conversation', [
                'conversation_id' => $conversationId,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Determine the last activity time for the conversation
     *
     * @param Conversation $conversation
     * @param \App\Domains\ChatBot\Interaction|null $lastInteraction
     * @return Carbon
     */
    private function getLastActivityTime(Conversation $conversation, ?\App\Domains\ChatBot\Interaction $lastInteraction): Carbon
    {
        // If there's a last interaction, use its created_at time
        if ($lastInteraction && $lastInteraction->created_at) {
            return $lastInteraction->created_at;
        }

        // If no interactions, use conversation's updated_at or created_at
        if ($conversation->updated_at) {
            return $conversation->updated_at;
        }

        return $conversation->created_at ?? Carbon::now();
    }

    /**
     * Check if conversation is inactive based on last activity and timeout
     *
     * @param Carbon $lastActivityTime
     * @param int $inactivityMinutes
     * @return bool
     */
    private function isConversationInactive(Carbon $lastActivityTime, int $inactivityMinutes): bool
    {
        $cutoffTime = Carbon::now()->subMinutes($inactivityMinutes);
        return $lastActivityTime->isBefore($cutoffTime);
    }

    /**
     * Close the conversation and update its status
     *
     * @param Conversation $conversation
     * @param Carbon $lastActivityTime
     * @param int $inactivityMinutes
     * @return bool
     */
    private function closeConversation(Conversation $conversation, Carbon $lastActivityTime, int $inactivityMinutes): bool
    {
        try {
            // Mark conversation as finished
            $conversation->is_finished = true;
            $conversation->updated_at = Carbon::now();

            // Add closure information to conversation JSON
            $conversationData = $conversation->json ? json_decode($conversation->json, true) : [];
            $conversationData['closure_info'] = [
                'closed_at' => Carbon::now()->toISOString(),
                'reason' => 'inactivity_timeout',
                'last_activity_at' => $lastActivityTime->toISOString(),
                'inactivity_minutes' => $inactivityMinutes,
                'closed_by' => 'system_cron'
            ];
            $conversation->json = json_encode($conversationData);

            // Save the conversation
            $this->conversationRepository->update($conversation, $conversation->organization_id);

            \Log::info('Conversation closed due to inactivity', [
                'conversation_id' => $conversation->id,
                'organization_id' => $conversation->organization_id,
                'client_id' => $conversation->client_id,
                'flow_id' => $conversation->flow_id,
                'last_activity_at' => $lastActivityTime->toISOString(),
                'inactivity_minutes' => $inactivityMinutes,
                'closed_at' => Carbon::now()->toISOString()
            ]);

            return true;
        } catch (Exception $e) {
            \Log::error('Failed to close inactive conversation', [
                'conversation_id' => $conversation->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw new Exception("Failed to close conversation {$conversation->id}: " . $e->getMessage());
        }
    }
}
