<?php

namespace App\UseCases\ChatBot\Conversation;

use App\Domains\ChatBot\Conversation;
use App\Domains\ChatBot\Interaction;
use App\Repositories\ConversationRepository;
use App\Repositories\InteractionRepository;
use App\UseCases\ChatBot\Conversation\Close;
use Carbon\Carbon;
use Exception;

class CloseInactiveConversation
{
    private ConversationRepository $conversationRepository;
    private InteractionRepository $interactionRepository;
    private Close $closeConversationUseCase;

    public function __construct(
        ConversationRepository $conversationRepository,
        InteractionRepository $interactionRepository,
        Close $closeConversationUseCase
    ) {
        $this->conversationRepository = $conversationRepository;
        $this->interactionRepository = $interactionRepository;
        $this->closeConversationUseCase = $closeConversationUseCase;
    }

    /**
     * Close conversation if it's inactive based on flow's inactivity_minutes
     *
     * @param Conversation $conversation
     * @return bool Returns true if conversation was closed, false if still active
     * @throws Exception
     */
    public function perform(Conversation $conversation): bool
    {
        if ($conversation->is_finished) {
            return false;
        }

        if (!$conversation->flow) {
            return false;
        }

        $inactivityMinutes = $conversation->flow->getTimeoutMinutes();

        $lastInteraction = $this->interactionRepository->getLastByConversationId($conversation->id);

        $lastActivityTime = $this->getLastActivityTime($conversation, $lastInteraction);

        if ($conversation->isConversationInactive($lastActivityTime, $inactivityMinutes)) {
            $this->closeConversationUseCase->perform($conversation, $lastActivityTime, $inactivityMinutes);
            return true;
        }

        return false;
    }

    /**
     * Determine the last activity time for the conversation
     *
     * @param Conversation $conversation
     * @param Interaction|null $lastInteraction
     * @return Carbon
     */
    private function getLastActivityTime(Conversation $conversation, ?Interaction $lastInteraction): Carbon
    {
        if ($lastInteraction && $lastInteraction->created_at) {
            return $lastInteraction->created_at;
        }

        if ($conversation->updated_at) {
            return $conversation->updated_at;
        }

        return $conversation->created_at ?? Carbon::now();
    }
}
