<?php

namespace App\UseCases\ChatBot\Conversation;

use App\Domains\ChatBot\Conversation;
use App\Repositories\InteractionRepository;
use Carbon\Carbon;

class GetLastActivityTime
{
    private InteractionRepository $interactionRepository;

    public function __construct(InteractionRepository $interactionRepository)
    {
        $this->interactionRepository = $interactionRepository;
    }

    /**
     * Get the last activity time for a conversation
     *
     * @param Conversation $conversation
     * @return Carbon
     */
    public function perform(Conversation $conversation): Carbon
    {
        $lastInteraction = $this->interactionRepository->getLastByConversationId($conversation->id);

        if ($lastInteraction && $lastInteraction->created_at) {
            return $lastInteraction->created_at;
        }

        if ($conversation->updated_at) {
            return $conversation->updated_at;
        }

        return $conversation->created_at ?? Carbon::now()->subHours(24);
    }
}
