<?php

namespace App\UseCases\ChatBot\ExchangedMessage;

use App\Domains\ChatBot\ExchangedMessage;
use App\Repositories\ExchangedMessageRepository;

class Get
{
    private ExchangedMessageRepository $exchangedMessageRepository;

    public function __construct(ExchangedMessageRepository $exchangedMessageRepository)
    {
        $this->exchangedMessageRepository = $exchangedMessageRepository;
    }

    /**
     * @param int $id
     * @return ExchangedMessage
     */
    public function perform(int $id): ExchangedMessage
    {
        return $this->exchangedMessageRepository->fetchById(
            $id,
            request()->user()->organization_id
        );
    }
}
