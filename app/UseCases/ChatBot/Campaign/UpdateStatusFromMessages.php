<?php

namespace App\UseCases\ChatBot\Campaign;

use App\Repositories\CampaignRepository;
use App\Repositories\MessageRepository;
use App\Repositories\CampaignStatusHistoryRepository;
use App\Enums\CampaignStatus;
use App\Enums\MessageStatus;
use App\Helpers\DBLog;

class UpdateStatusFromMessages
{
    private CampaignRepository $campaignRepository;
    private MessageRepository $messageRepository;
    private CampaignStatusHistoryRepository $statusHistoryRepository;

    public function __construct(
        CampaignRepository $campaignRepository,
        MessageRepository $messageRepository,
        CampaignStatusHistoryRepository $statusHistoryRepository
    ) {
        $this->campaignRepository = $campaignRepository;
        $this->messageRepository = $messageRepository;
        $this->statusHistoryRepository = $statusHistoryRepository;
    }

    public function execute(int $campaign_id, ?int $user_id = null): bool
    {
        try {
            // Fetch campaign
            $campaign = $this->campaignRepository->fetchById($campaign_id);
            $oldStatus = $campaign->getCurrentStatus();

            // Skip if campaign is already finished
            if ($oldStatus->isFinished()) {
                return false;
            }

            // Get message statistics for this campaign
            $messageStats = $this->getMessageStatistics($campaign_id);
            
            // Determine new status based on message statistics
            $newStatus = $this->determineStatusFromMessages($oldStatus, $messageStats);

            // No change needed
            if ($oldStatus === $newStatus) {
                return false;
            }

            // Update campaign status
            $campaign->updateStatus($newStatus);
            $this->campaignRepository->save($campaign, $campaign->organization_id);

            // Create status history record
            $this->statusHistoryRepository->createStatusChange(
                $campaign_id,
                $oldStatus,
                $newStatus,
                'Status updated based on message delivery results',
                $user_id,
                ['message_stats' => $messageStats]
            );

            DBLog::logInfo(
                "Campaign status updated from messages",
                "Campaign::UpdateStatusFromMessages",
                $campaign->organization_id,
                $user_id,
                [
                    'campaign_id' => $campaign_id,
                    'old_status' => $oldStatus->label(),
                    'new_status' => $newStatus->label(),
                    'message_stats' => $messageStats
                ]
            );

            return true;

        } catch (\Throwable $e) {
            DBLog::logError(
                $e->getMessage(),
                "Campaign::UpdateStatusFromMessages",
                null,
                $user_id,
                ['campaign_id' => $campaign_id]
            );
            throw $e;
        }
    }

    private function getMessageStatistics(int $campaign_id): array
    {
        // This would need to be implemented in MessageRepository
        // For now, we'll return a mock structure
        return [
            'total' => 0,
            'sent' => 0,
            'failed' => 0,
            'pending' => 0,
            'sending' => 0
        ];
    }

    private function determineStatusFromMessages(CampaignStatus $currentStatus, array $messageStats): CampaignStatus
    {
        $total = $messageStats['total'];
        $sent = $messageStats['sent'];
        $failed = $messageStats['failed'];
        $sending = $messageStats['sending'];

        // If no messages, keep current status
        if ($total === 0) {
            return $currentStatus;
        }

        // If still sending messages
        if ($sending > 0) {
            return CampaignStatus::SENDING;
        }

        // If all messages processed
        if ($sent + $failed >= $total) {
            // If more than 50% failed, consider campaign failed
            if ($failed > ($total * 0.5)) {
                return CampaignStatus::FAILED;
            }
            // Otherwise, completed
            return CampaignStatus::COMPLETED;
        }

        // Default to current status
        return $currentStatus;
    }
}
