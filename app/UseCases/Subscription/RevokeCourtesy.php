<?php

namespace App\UseCases\Subscription;

use App\Domains\Subscription;
use App\Repositories\SubscriptionRepository;
use App\Repositories\OrganizationRepository;
use App\Factories\SubscriptionFactory;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class RevokeCourtesy
{
    private SubscriptionRepository $subscriptionRepository;
    private OrganizationRepository $organizationRepository;
    private SubscriptionFactory $subscriptionFactory;

    public function __construct(
        SubscriptionRepository $subscriptionRepository,
        OrganizationRepository $organizationRepository,
        SubscriptionFactory $subscriptionFactory
    ) {
        $this->subscriptionRepository = $subscriptionRepository;
        $this->organizationRepository = $organizationRepository;
        $this->subscriptionFactory = $subscriptionFactory;
    }

    public function perform(int $organizationId, string $reason): Subscription
    {
        DB::beginTransaction();

        try {
            $organization = $this->organizationRepository->fetchById($organizationId);
            if (!$organization) {
                throw new \InvalidArgumentException('Organization not found');
            }

            $subscription = $this->subscriptionRepository->findByOrganizationId($organizationId);

            if (!$subscription) {
                throw new \InvalidArgumentException('No subscription found for organization');
            }

            if (!$subscription->is_courtesy) {
                throw new \InvalidArgumentException('Subscription is not a courtesy');
            }

            $updatedSubscription = $this->subscriptionFactory->buildFromStoreArray([
                'organization_id' => $subscription->organization_id,
                'type' => 'trial',
                'status' => 'inactive',
                'value' => $subscription->value,
                'started_at' => $subscription->started_at,
                'expires_at' => $subscription->expires_at,
                'is_courtesy' => false,
                'courtesy_expires_at' => Carbon::now(),
                'courtesy_reason' => $subscription->courtesy_reason . ' | Revoked: ' . $reason,
            ]);

            $updatedSubscription = $this->subscriptionRepository->update($updatedSubscription);

            DB::commit();

            Log::info('Courtesy revoked successfully', [
                'organization_id' => $organizationId,
                'subscription_id' => $subscription->id,
                'reason' => $reason,
            ]);

            return $updatedSubscription;

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('Failed to revoke courtesy', [
                'organization_id' => $organizationId,
                'error' => $e->getMessage(),
            ]);

            throw $e;
        }
    }
}
