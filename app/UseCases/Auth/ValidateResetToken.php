<?php

namespace App\UseCases\Auth;

use App\Helpers\DBLog;
use App\Http\Requests\Auth\ValidateTokenRequest;
use App\Repositories\PasswordResetTokenRepository;

class ValidateResetToken
{
    private PasswordResetTokenRepository $tokenRepository;

    public function __construct(PasswordResetTokenRepository $tokenRepository)
    {
        $this->tokenRepository = $tokenRepository;
    }

    /**
     * Validate a password reset token
     */
    public function perform(ValidateTokenRequest $request): bool
    {
        $email = $request->email;
        $plainToken = $request->token;
        $ipAddress = $request->ip();

        // Log the validation attempt
        DBLog::log(
            "Password reset token validation attempted for email: {$email}",
            "Auth\\ValidateResetToken",
            null,
            null,
            [
                'email' => $email,
                'ip_address' => $ipAddress
            ]
        );

        // Find the token
        $token = $this->tokenRepository->findByEmailAndToken($email, $plainToken);

        if (!$token) {
            DBLog::log(
                "Password reset token validation failed - token not found for email: {$email}",
                "Auth\\ValidateResetToken",
                null,
                null,
                [
                    'email' => $email,
                    'ip_address' => $ipAddress,
                    'result' => 'token_not_found'
                ]
            );
            
            throw new \Exception('Token inválido ou expirado.');
        }

        // Check if token is valid
        if (!$token->isValid()) {
            $reason = $token->isExpired() ? 'expired' : 'used';
            
            DBLog::log(
                "Password reset token validation failed - token {$reason} for email: {$email}",
                "Auth\\ValidateResetToken",
                $token->organization_id,
                null,
                [
                    'email' => $email,
                    'ip_address' => $ipAddress,
                    'token_id' => $token->id,
                    'result' => "token_{$reason}"
                ]
            );
            
            throw new \Exception('Token inválido ou expirado.');
        }

        // Token is valid
        DBLog::log(
            "Password reset token validation successful for email: {$email}",
            "Auth\\ValidateResetToken",
            $token->organization_id,
            null,
            [
                'email' => $email,
                'ip_address' => $ipAddress,
                'token_id' => $token->id,
                'result' => 'valid'
            ]
        );

        return true;
    }
}
