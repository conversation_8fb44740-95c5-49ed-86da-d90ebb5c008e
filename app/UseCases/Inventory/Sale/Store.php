<?php

namespace App\UseCases\Inventory\Sale;

use App\Domains\Inventory\Sale;
use App\Factories\Inventory\SaleFactory;
use App\Helpers\DBLog;
use App\Http\Requests\Sale\StoreRequest;
use App\Repositories\SaleRepository;
use App\Services\ASAAS\Exceptions\AsaasException;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class Store
{
    private SaleRepository $saleRepository;
    private SaleFactory $saleFactory;

    public function __construct(
        SaleRepository $saleRepository,
        SaleFactory $saleFactory,
    ) {
        $this->saleRepository = $saleRepository;
        $this->saleFactory = $saleFactory;
    }

    /**
     * @param StoreRequest $request
     * @return Sale
     */
    public function perform(StoreRequest $request) : Sale {
        DB::beginTransaction();

        try {
            $domain = $this->saleFactory->buildFromStoreRequest($request);
            $domain->organization_id = request()->user()->organization_id;

            $sale = $this->saleRepository->store($domain);

            // Check if ASAAS integration should be created
            if ($request->input('enable_asaas_integration', false)) {
                // TODO: Create ASAAS payment
                DBLog::log('ASAAS payment created automatically',
                    "Sale::Store",
                    $sale->organization_id, auth()->user()->id ?? null, [
                    'sale_id' => $sale->id,
                    'organization_id' => $sale->organization_id
                ]);
            }

            DB::commit();
            return $sale;

        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }
}
