<?php

namespace App\UseCases\Organization;

use App\Domains\Organization;
use App\Factories\OrganizationFactory;
use App\Http\Requests\Organization\UpdateRequest;
use App\Repositories\OrganizationRepository;
use Illuminate\Support\Facades\DB;
use Exception;

class Update
{
    private OrganizationRepository $organizationRepository;
    private OrganizationFactory $organizationFactory;

    public function __construct(
        OrganizationRepository $organizationRepository,
        OrganizationFactory $organizationFactory
    ) {
        $this->organizationRepository = $organizationRepository;
        $this->organizationFactory = $organizationFactory;
    }

    /**
     * @param UpdateRequest $request
     * @param int $id
     * @return Organization
     * @throws Exception
     */
    public function perform(UpdateRequest $request, int $id) : Organization {
        $userOrganizationId = request()->user()->organization_id;
        if ($userOrganizationId !== $id) {
            throw new Exception('You can only update your own organization.');
        }

        try {
            DB::beginTransaction();

            $domain = $this->organizationFactory->buildFromUpdateRequest($request);
            $domain->id = $id;

            $organization = $this->organizationRepository->update($domain);

            DB::commit();

            return $organization;
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }
}
