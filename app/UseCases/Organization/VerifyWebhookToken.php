<?php

namespace App\UseCases\Organization;

use App\Repositories\OrganizationRepository;

class VerifyWebhookToken
{
    private OrganizationRepository $organizationRepository;

    public function __construct(OrganizationRepository $organizationRepository)
    {
        $this->organizationRepository = $organizationRepository;
    }

    /**
     * Verify webhook token with organization priority
     *
     * @param string $mode
     * @param string $token
     * @return array
     */
    public function perform(string $mode, string $token): array
    {
        if ($mode !== 'subscribe') {
            return ['success' => false, 'error' => 'Invalid mode'];
        }

        // Prioridade 1: Token de organização
        $organization = $this->organizationRepository->fetchByWebhookToken($token);
        if ($organization) {
            return [
                'success' => true,
                'organization' => $organization,
                'type' => 'organization'
            ];
        }

        // Prioridade 2: Token global
        if ($token === config('whatsapp.webhook_verify_token')) {
            return [
                'success' => true,
                'organization' => null,
                'type' => 'global'
            ];
        }

        return ['success' => false, 'error' => 'Invalid token'];
    }
}
