<?php

namespace App\Domains\Imports;

use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\Importable;
use App\Models\Client;

class ImportClient extends Core implements ToCollection
{
    use Importable;

    public bool $skipHeader = false;
    public array $columns_sql_to_fill = [
        'name',
        'phone',
        'email',
        'profession',
        'birthdate',
        'cpf',
        'cnpj',
        'service',
        'address',
        'number',
        'neighborhood',
        'cep',
        'complement',
        'civil_state',
        'description',
    ];

    public function collection(Collection $collection) : void {
        $rows = $this->skipHeader ? $collection->skip(1) : $collection;
        foreach ($rows as $row) {
            Client::create([
                'organization_id' => $this->organization_id,
                'name' => $this->getRowIndexFromMap($row, 'name'),
                'phone' => $this->getRowIndexFromMap($row, 'phone'),
                'email' => $this->getRowIndexFromMap($row, 'email'),
                'profession' => $this->getRowIndexFromMap($row, 'profession'),
                'birthdate' => $this->getRowIndexFromMap($row, 'birthdate'),
                'cpf' => $this->getRowIndexFromMap($row, 'cpf'),
                'cnpj' => $this->getRowIndexFromMap($row, 'cnpj'),
                'service' => $this->getRowIndexFromMap($row, 'service'),
                'address' => $this->getRowIndexFromMap($row, 'address'),
                'number' => $this->getRowIndexFromMap($row, 'number'),
                'neighborhood' => $this->getRowIndexFromMap($row, 'neighborhood'),
                'cep' => $this->getRowIndexFromMap($row, 'cep'),
                'complement' => $this->getRowIndexFromMap($row, 'complement'),
                'civil_state' => $this->getRowIndexFromMap($row, 'civil_state'),
                'description' => $this->getRowIndexFromMap($row, 'description'),
            ]);
        }
    }
}
