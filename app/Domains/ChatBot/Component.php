<?php

namespace App\Domains\ChatBot;

use App\Enums\ChatBot\ComponentFormat;
use App\Factories\ChatBot\ParameterFactory;
use App\Services\VariableSubstitution\VariableSubstitutionService;
use Carbon\Carbon;

class Component
{
    private const string HEADER_TYPE = "HEADER";
    private const string BODY_TYPE = "BODY";
    private const string FOOTER_TYPE = "FOOTER";
    private const string BUTTONS_TYPE = "BUTTONS";

    public ?int $id;
    public ?int $organization_id;
    public ?int $step_id;
    public ?int $template_id;
    public ?string $name;
    public ?string $type;
    public ?string $sub_type;
    public ?int $index;
    public ?string $text;
    public ?ComponentFormat $format; // TEXT | IMAGE | VIDEO | DOCUMENT
    public ?string $json;
    public ?Carbon $created_at;
    public ?Carbon $updated_at;
    public ?Step $step;
    public ?Template $template;

    /** @var Button[]|null $buttons */
    public ?array $buttons;

    /** @var Parameter[]|null $parameters */
    public ?array $parameters;

    private array $placeholders = [];

    public function __construct(
        ?int $id,
        ?int $organization_id,
        ?int $step_id,
        ?int $template_id,
        ?string $name,
        ?string $type,
        ?string $sub_type,
        ?int $index,
        ?string $text,
        ?ComponentFormat $format,
        ?string $json,
        ?Carbon $created_at = null,
        ?Carbon $updated_at = null,
        ?Step $step = null,
        ?Template $template = null,
        ?array $buttons = null,
        ?array $parameters = null
    ){
        $this->id = $id;
        $this->organization_id = $organization_id;
        $this->step_id = $step_id;
        $this->template_id = $template_id;
        $this->name = $name;
        $this->type = $type;
        $this->sub_type = $sub_type;
        $this->index = $index;
        $this->text = $text;
        $this->format = $format ?? ComponentFormat::TEXT;
        $this->json = $json;
        $this->created_at = $created_at;
        $this->updated_at = $updated_at;
        $this->step = $step;
        $this->template = $template;
        $this->buttons = $buttons;
        $this->parameters = $parameters;
    }

    public function toArray(): array
    {
        return [
            "id" => $this->id,
            "organization_id" => $this->organization_id,
            "step_id" => $this->step_id,
            "template_id" => $this->template_id,
            "name" => $this->name,
            "type" => $this->type,
            "sub_type" => $this->sub_type,
            "index" => $this->index,
            "format" => $this->format?->value,
            "text" => $this->text,
            "json" => $this->json,
            "created_at" => ($this->created_at ?? Carbon::now())->format("Y-m-d H:i:s"),
            "updated_at" => ($this->updated_at ?? Carbon::now())->format("Y-m-d H:i:s"),
            "step" => $this->step?->toArray(),
            "template" => $this->template?->toArray(),
            "buttons" => $this->buttons,
            "parameters" => $this->parameters,
        ];
    }

    public function toStoreArray(): array
    {
        return [
            "organization_id" => $this->organization_id,
            "step_id" => $this->step_id,
            "template_id" => $this->template_id,
            "name" => $this->name,
            "type" => $this->type,
            "sub_type" => $this->sub_type,
            "index" => $this->index,
            "format" => $this->format?->value,
            "text" => $this->text,
            "json" => $this->json,
        ];
    }

    public function toUpdateArray(): array
    {
        return [
            "step_id" => $this->step_id,
            "template_id" => $this->template_id,
            "name" => $this->name,
            "type" => $this->type,
            "sub_type" => $this->sub_type,
            "index" => $this->index,
            "format" => $this->format?->value,
            "text" => $this->text,
            "json" => $this->json,
        ];
    }

    public function toWhatsAppMessagePayload(array $availableModels = []): ?array
    {
        $type = strtolower($this->type ?? '');
        if ($type !== 'body') { return null; }

        $parameters = $this->extractParametersFromText($availableModels);
        if (empty($parameters)) { return null; }

        return [
            'type' => $type,
            'parameters' => $parameters,
        ];
    }

    public function toWhatsAppTemplatePayload(): array
    {
        return match ($this->type) {
            self::HEADER_TYPE => $this->toHeaderWhatsAppTemplatePayload(),
            self::BODY_TYPE => $this->toBodyWhatsAppTemplatePayload(),
            self::FOOTER_TYPE => $this->toFooterWhatsAppTemplatePayload(),
            self::BUTTONS_TYPE => $this->toButtonsWhatsAppPayload(),
            default => json_decode($this->json, true),
        };
    }

    public function toHeaderWhatsAppTemplatePayload(): array
    {
        return [
            'type' => (self::HEADER_TYPE),
            'format' => ($this->format->value),
            'text' => $this->convertVariablesToPlaceholders($this->text ?? ''),
        ];
    }

    public function toHeaderWhatsAppPayload(array $availableModels = []): array
    {
        return [
            'type' => strtolower(self::HEADER_TYPE),
            'format' => strtolower($this->format->value),
            'text' => $this->getProcessedText($availableModels),
        ];
    }

    public function toBodyWhatsAppTemplatePayload(): array
    {
        $convertedText = $this->convertVariablesToPlaceholders($this->text ?? '');

        return [
            'type' => (self::BODY_TYPE),
            'text' => $convertedText,
        ];
    }

    public function toBodyWhatsAppPayload(string $type, array $availableModels = []): array
    {
        return [
            'type' => strtolower($type),
            'text' => $this->getProcessedText($availableModels),
            'parameters' => collect($this->parameters ?? [])
                ->map(function ($parameter) use ($availableModels) {
                    $value = $parameter->value ?? '';
                    // Apply variable substitution to parameter value
                    if (!empty($availableModels)) {
                        $substitutionService = new VariableSubstitutionService();
                        $value = $substitutionService->substitute($value, $availableModels);
                    }
                    return ['type' => 'text', 'text' => $value];
                })->toArray(),
        ];
    }

    public function toFooterWhatsAppTemplatePayload(): array
    {
        return [
            'type' => (self::FOOTER_TYPE),
            'text' => $this->convertVariablesToPlaceholders($this->text ?? ''),
        ];
    }

    public function toFooterWhatsAppPayload(string $type, array $availableModels = []): array
    {
        return [
            'type' => strtolower($type),
            'text' => $this->getProcessedText($availableModels),
        ];
    }

    /**
     * Get processed text with variable substitution
     *
     * @param array $availableModels
     * @return string
     */
    public function getProcessedText(array $availableModels = []): string
    {
        if ($this->type && strtolower($this->type) === strtolower(self::HEADER_TYPE)) {
            return $this->text ?? '';
        }

        $text = $this->text ?? '';

        if (empty($availableModels)) {
            return $text;
        }

        $substitutionService = new VariableSubstitutionService();
        return $substitutionService->substitute($text, $availableModels);
    }
    public function toButtonsWhatsAppPayload() : array
    {
        $buttons = [];
        foreach($this->buttons ?? [] as $button){
            $buttons[] = $button->toWhatsAppPayload();
        }
        return [
            'type' => (self::BUTTONS_TYPE),
            'buttons' => $buttons,
        ];
    }
    public function toButtonsWhatsAppPayloadBKP(): array
    {
        $payload = ['type' => strtolower(self::BUTTONS_TYPE)];
        if ($this->sub_type){
            $payload['sub_type'] = $this->sub_type;
        }
        if ($this->index !== null){
            $payload['index'] = (int) $this->index;
        }
        $payload['parameters'] = $this->getButtonParameters();
        if(count($payload['parameters']) == 0){
            unset($payload['parameters']);
        }
        if ($this->text){
            $payload['text'] = $this->text;
        }
        return $payload;
    }

    public function validateForWhatsApp() : bool {
        /** @var Button $button */
        foreach($this->buttons ?? [] as $button){
            if(!$button->validateForWhatsApp()){
                return true;
            }
        }
        return true;
    }

    public function buildParameters() : array
    {
        foreach($this->placeholders ?? [] as $placeholder){
            /** @var ParameterFactory $parameterFactory */
            $parameterFactory = app()->make(ParameterFactory::class);
            $this->parameters[] = $parameterFactory->buildFromComponent($this, $placeholder);
        }
        return $this->parameters ?? [];
    }

    /**
     * Convert variables like {{client.name}} to numbered placeholders {{1}}
     * Used for WhatsApp template registration
     */
    public function convertVariablesToPlaceholders(string $text): string
    {
        preg_match_all('/{{\s*([a-zA-Z_]+\.[a-zA-Z_]+)\s*}}/', $text, $matches);

        $variables = $matches[1];
        $mapping = [];
        $normalizedText = $text;

        foreach ($variables as $index => $original) {
            $placeholderNumber = $index + 1;
            $mapping[] = $original;

            // Replace one by one
            $normalizedText = preg_replace('/{{\s*' . preg_quote($original, '/') . '\s*}}/', '{{' . $placeholderNumber . '}}', $normalizedText, 1);
        }

        $this->placeholders = $mapping;

        return $normalizedText;
    }

    /**
     * @deprecated Use convertVariablesToPlaceholders instead
     */
    public function textToWhatsAppPayload(): string
    {
        return $this->convertVariablesToPlaceholders($this->text ?? '');
    }

    /**
     * Get the variable mapping for this component
     */
    public function getVariableMapping(): array
    {
        return $this->placeholders;
    }

    /**
     * Extract parameters from text for template message payload
     */
    private function extractParametersFromText(array $availableModels = []): array
    {
        $text = $this->text ?? '';
        $substitutionService = new VariableSubstitutionService();

        // Find all variables in the text
        $variables = $substitutionService->getVariablesFromText($text);
        $parameters = [];

        foreach ($variables as $variable) {
            $modelName = $variable['model'];
            $variableName = $variable['variable'];

            // Get the actual value from available models
            $value = '';
            if (isset($availableModels[$modelName]) && $availableModels[$modelName]) {
                $model = $availableModels[$modelName];
                if (is_object($model)) {
                    $modelArray = $model->toArray();
                } else {
                    $modelArray = $model;
                }

                $value = $modelArray[$variableName] ?? '';
            }

            $parameters[] = [
                'type' => 'text',
                'text' => (string) $value
            ];
        }

        return $parameters;
    }

    /**
     * Get button index from parameters
     */
    private function getButtonIndex(): int
    {
        // Find parameter with type 'button' and return its index
        foreach ($this->parameters ?? [] as $parameter) {
            if ($parameter->type === 'button' && $parameter->index !== null) {
                return $parameter->index;
            }
        }
        return 0; // Default index
    }

    /**
     * Get button parameters for WhatsApp payload
     */
    private function getButtonParameters(): array
    {
        $parameters = [];

        foreach ($this->parameters ?? [] as $parameter) {
            if ($parameter->type !== 'button') {
                $paramArray = [
                    'type' => $parameter->type ?? 'text'
                ];

                // Add appropriate field based on parameter type
                switch ($parameter->type) {
                    case 'phone_number':
                        $paramArray['phone_number'] = $parameter->value;
                        break;
                    case 'text':
                    default:
                        $paramArray['text'] = $parameter->value;
                        break;
                }

                $parameters[] = $paramArray;
            }
        }

        return $parameters;
    }
}
