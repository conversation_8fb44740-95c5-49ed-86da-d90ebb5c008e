<?php

namespace App\Domains\ChatBot;

use App\Domains\Inventory\Client;
use Carbon\Carbon;

class ExchangedMessage
{
    public ?int $id;
    public ?int $organization_id;
    public ?int $client_id;
    public ?int $phone_number_id;
    public ?int $conversation_id;
    public ?int $webhook_log_id;
    public ?int $message_id;

    public ?bool $inbound;
    public ?bool $outbound;
    public ?string $message;
    public ?array $json;
    public ?Carbon $sent_at;

    public ?Carbon $created_at;
    public ?Carbon $updated_at;

    // Related objects
    public ?Client $client;
    public ?PhoneNumber $phone_number;
    public ?Conversation $conversation;
    public ?Message $message_object;

    public function __construct(
        ?int $id = null,
        ?int $organization_id = null,
        ?int $client_id = null,
        ?int $phone_number_id = null,
        ?int $conversation_id = null,
        ?int $webhook_log_id = null,
        ?int $message_id = null,
        ?bool $inbound = false,
        ?bool $outbound = false,
        ?string $message = null,
        ?array $json = null,
        ?Carbon $sent_at = null,
        ?Carbon $created_at = null,
        ?Carbon $updated_at = null,
        ?Client $client = null,
        ?PhoneNumber $phone_number = null,
        ?Conversation $conversation = null,
        ?Message $message_object = null
    ) {
        $this->id = $id;
        $this->organization_id = $organization_id;
        $this->client_id = $client_id;
        $this->phone_number_id = $phone_number_id;
        $this->conversation_id = $conversation_id;
        $this->webhook_log_id = $webhook_log_id;
        $this->message_id = $message_id;
        $this->inbound = $inbound;
        $this->outbound = $outbound;
        $this->message = $message;
        $this->json = $json;
        $this->sent_at = $sent_at;
        $this->created_at = $created_at;
        $this->updated_at = $updated_at;
        $this->client = $client;
        $this->phone_number = $phone_number;
        $this->conversation = $conversation;
        $this->message_object = $message_object;
    }

    public function toArray(): array
    {
        return [
            "id" => $this->id,
            "organization_id" => $this->organization_id,
            "client_id" => $this->client_id,
            "phone_number_id" => $this->phone_number_id,
            "conversation_id" => $this->conversation_id,
            "webhook_log_id" => $this->webhook_log_id,
            "message_id" => $this->message_id,
            "inbound" => $this->inbound,
            "outbound" => $this->outbound,
            "message" => $this->message,
            "json" => $this->json,
            "sent_at" => $this->sent_at?->format("Y-m-d H:i:s"),
            "created_at" => ($this->created_at ?? Carbon::now())->format("Y-m-d H:i:s"),
            "updated_at" => ($this->updated_at ?? Carbon::now())->format("Y-m-d H:i:s"),
            "client" => $this->client?->toArray(),
            "phone_number" => $this->phone_number?->toArray(),
            "conversation" => $this->conversation?->toArray(),
            "message_object" => $this->message_object?->toArray(),
        ];
    }

    public function toStoreArray(): array
    {
        return [
            "organization_id" => $this->organization_id,
            "client_id" => $this->client_id,
            "phone_number_id" => $this->phone_number_id,
            "conversation_id" => $this->conversation_id,
            "webhook_log_id" => $this->webhook_log_id,
            "message_id" => $this->message_id,
            "inbound" => $this->inbound,
            "outbound" => $this->outbound,
            "message" => $this->message,
            "json" => $this->json,
            "sent_at" => $this->sent_at?->format("Y-m-d H:i:s"),
        ];
    }

    public function toUpdateArray(): array
    {
        return [
            "client_id" => $this->client_id,
            "phone_number_id" => $this->phone_number_id,
            "conversation_id" => $this->conversation_id,
            "webhook_log_id" => $this->webhook_log_id,
            "message_id" => $this->message_id,
            "inbound" => $this->inbound,
            "outbound" => $this->outbound,
            "message" => $this->message,
            "json" => $this->json,
            "sent_at" => $this->sent_at?->format("Y-m-d H:i:s"),
        ];
    }

    /**
     * Check if this is an inbound message
     */
    public function isInbound(): bool
    {
        return $this->inbound === true;
    }

    /**
     * Check if this is an outbound message
     */
    public function isOutbound(): bool
    {
        return $this->outbound === true;
    }

    /**
     * Get the direction of the message
     */
    public function getDirection(): string
    {
        if ($this->isInbound()) {
            return 'inbound';
        }

        if ($this->isOutbound()) {
            return 'outbound';
        }

        return 'unknown';
    }
}
