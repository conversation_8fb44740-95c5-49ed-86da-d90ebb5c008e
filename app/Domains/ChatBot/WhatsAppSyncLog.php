<?php

namespace App\Domains\ChatBot;

use App\Enums\SyncType;
use App\Enums\SyncStatus;
use Carbon\Carbon;

class WhatsAppSyncLog
{
    public ?int $id;
    public ?SyncType $sync_type;
    public ?int $entity_id;
    public ?SyncStatus $status;
    public ?array $response_data_json;
    public ?string $error_message;
    public ?int $messages_synced;
    public ?int $messages_updated;
    public ?Carbon $synced_at;
    public ?Carbon $created_at;
    public ?Carbon $updated_at;

    public function __construct(
        ?int $id = null,
        ?SyncType $sync_type = null,
        ?int $entity_id = null,
        ?SyncStatus $status = null,
        ?array $response_data_json = null,
        ?string $error_message = null,
        ?int $messages_synced = 0,
        ?int $messages_updated = 0,
        ?Carbon $synced_at = null,
        ?Carbon $created_at = null,
        ?Carbon $updated_at = null
    ) {
        $this->id = $id;
        $this->sync_type = $sync_type;
        $this->entity_id = $entity_id;
        $this->status = $status;
        $this->response_data_json = $response_data_json;
        $this->error_message = $error_message;
        $this->messages_synced = $messages_synced;
        $this->messages_updated = $messages_updated;
        $this->synced_at = $synced_at ?? now();
        $this->created_at = $created_at;
        $this->updated_at = $updated_at;
    }

    public function toStoreArray(): array
    {
        return [
            'sync_type' => $this->sync_type?->value,
            'entity_id' => $this->entity_id,
            'status' => $this->status?->value,
            'response_data_json' => $this->response_data_json,
            'error_message' => $this->error_message,
            'messages_synced' => $this->messages_synced,
            'messages_updated' => $this->messages_updated,
            'synced_at' => $this->synced_at,
        ];
    }

    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'sync_type' => $this->sync_type?->value,
            'sync_type_label' => $this->sync_type?->label(),
            'entity_id' => $this->entity_id,
            'status' => $this->status?->value,
            'status_label' => $this->status?->label(),
            'status_color' => $this->status?->color(),
            'response_data_json' => $this->response_data_json,
            'error_message' => $this->error_message,
            'messages_synced' => $this->messages_synced,
            'messages_updated' => $this->messages_updated,
            'synced_at' => $this->synced_at?->toISOString(),
            'created_at' => $this->created_at?->toISOString(),
            'updated_at' => $this->updated_at?->toISOString(),
            'duration_since_sync_minutes' => $this->getDurationSinceSync(),
            'was_successful' => $this->wasSuccessful(),
        ];
    }

    /**
     * Create a sync log entry
     */
    public static function create(
        SyncType $sync_type,
        int $entity_id,
        SyncStatus $status,
        ?array $response_data = null,
        ?string $error_message = null,
        int $messages_synced = 0,
        int $messages_updated = 0
    ): self {
        return new self(
            sync_type: $sync_type,
            entity_id: $entity_id,
            status: $status,
            response_data_json: $response_data,
            error_message: $error_message,
            messages_synced: $messages_synced,
            messages_updated: $messages_updated,
            synced_at: now()
        );
    }

    /**
     * Check if sync was successful
     */
    public function wasSuccessful(): bool
    {
        return $this->status === SyncStatus::SUCCESS;
    }

    /**
     * Check if sync failed
     */
    public function failed(): bool
    {
        return $this->status === SyncStatus::FAILED;
    }

    /**
     * Check if sync was partial
     */
    public function wasPartial(): bool
    {
        return $this->status === SyncStatus::PARTIAL;
    }

    /**
     * Get sync duration in minutes
     */
    public function getDurationSinceSync(): int
    {
        if (!$this->synced_at) {
            return 0;
        }
        return now()->diffInMinutes($this->synced_at);
    }

    /**
     * Get sync efficiency percentage
     */
    public function getSyncEfficiency(): float
    {
        if ($this->messages_synced === 0) {
            return 0.0;
        }
        return round(($this->messages_updated / $this->messages_synced) * 100, 2);
    }

    /**
     * Check if this is a recent sync (within last hour)
     */
    public function isRecent(): bool
    {
        return $this->getDurationSinceSync() <= 60;
    }

    /**
     * Get error summary for display
     */
    public function getErrorSummary(): ?string
    {
        if (!$this->error_message) {
            return null;
        }

        // Truncate long error messages
        if (strlen($this->error_message) > 100) {
            return substr($this->error_message, 0, 97) . '...';
        }

        return $this->error_message;
    }
}
