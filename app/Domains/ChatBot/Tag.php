<?php

namespace App\Domains\ChatBot;

use Carbon\Carbon;

class Tag
{
    public ?int $id;
    public ?int $organization_id;
    public ?string $name;
    public ?int $usage_count;
    public ?Carbon $created_at;
    public ?Carbon $updated_at;

    /** @var Campaign[]|null $campaigns */
    public ?array $campaigns;

    public function __construct(
        ?int $id = null,
        ?int $organization_id = null,
        ?string $name = null,
        ?int $usage_count = 0,
        ?Carbon $created_at = null,
        ?Carbon $updated_at = null,
        ?array $campaigns = null
    ) {
        $this->id = $id;
        $this->organization_id = $organization_id;
        $this->name = $name;
        $this->usage_count = $usage_count;
        $this->created_at = $created_at;
        $this->updated_at = $updated_at;
        $this->campaigns = $campaigns;
    }

    public function toStoreArray(): array
    {
        return [
            'organization_id' => $this->organization_id,
            'name' => $this->name,
            'usage_count' => $this->usage_count ?? 0,
        ];
    }

    public function toUpdateArray(): array
    {
        return [
            'name' => $this->name,
        ];
    }

    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'organization_id' => $this->organization_id,
            'name' => $this->name,
            'usage_count' => $this->usage_count,
            'created_at' => $this->created_at?->toISOString(),
            'updated_at' => $this->updated_at?->toISOString(),
            'campaigns' => $this->campaigns ? array_map(fn($campaign) => $campaign->toArray(), $this->campaigns) : null,
        ];
    }

    /**
     * Check if tag can be deleted (no campaigns assigned)
     */
    public function canBeDeleted(): bool
    {
        return $this->usage_count === 0 || ($this->campaigns && count($this->campaigns) === 0);
    }

    /**
     * Increment usage count
     */
    public function incrementUsage(): void
    {
        $this->usage_count = ($this->usage_count ?? 0) + 1;
    }

    /**
     * Decrement usage count
     */
    public function decrementUsage(): void
    {
        if (($this->usage_count ?? 0) > 0) {
            $this->usage_count--;
        }
    }

    /**
     * Normalize tag name for consistency
     */
    public function normalizeName(): void
    {
        if ($this->name) {
            $this->name = trim(strtolower($this->name));
        }
    }
}
