<?php

namespace App\Models;

use App\Enums\CampaignStatus;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Campaign extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'campaigns';

    protected $fillable = [
        'organization_id',
        'user_id',
        'template_id',
        'phone_number_id',
        'name',
        'description',
        'is_scheduled',
        'is_sent',
        'is_sending',
        'is_direct_message',
        'message_count',
        'status',
        'sent_at',
        'scheduled_at',
        'cancelled_at',
        'failed_at',
    ];

    protected $casts = [
        'status' => CampaignStatus::class,
        'sent_at' => 'datetime',
        'scheduled_at' => 'datetime',
        'cancelled_at' => 'datetime',
        'failed_at' => 'datetime',
    ];

    public function organization(){
        return $this->belongsTo(Organization::class);
    }
    public function user(){
        return $this->belongsTo(User::class);
    }
    public function template(){
        return $this->belongsTo(Template::class);
    }
    public function phoneNumber(){
        return $this->belongsTo(PhoneNumber::class);
    }
    public function messages(){
        return $this->hasMany(Message::class);
    }
    public function clients(){
        return $this->belongsToMany(Client::class, 'campaigns_clients');
    }
    public function parameters() {
        return $this->hasMany(Parameter::class);
    }

    public function categories()
    {
        return $this->belongsToMany(Category::class, 'campaigns_categories')
                    ->withTimestamps()
                    ->withPivot('assigned_at');
    }

    public function tags()
    {
        return $this->belongsToMany(Tag::class, 'campaign_tag_assignments')
                    ->withTimestamps()
                    ->withPivot('assigned_at');
    }

    public function categoryAssignments()
    {
        return $this->hasMany(CampaignCategoryAssignment::class);
    }

    public function tagAssignments()
    {
        return $this->hasMany(CampaignTagAssignment::class);
    }

    public function statusHistory()
    {
        return $this->hasMany(CampaignStatusHistory::class)->orderBy('created_at', 'desc');
    }

    /**
     * Get current status from enum or derive from boolean fields
     */
    public function getCurrentStatus(): CampaignStatus
    {
        if ($this->status) {
            return $this->status;
        }

        // Fallback to boolean fields for backward compatibility
        return CampaignStatus::fromBooleans(
            $this->is_sent ?? false,
            $this->is_sending ?? false,
            $this->is_scheduled ?? false,
            $this->hasFailedMessages(),
            $this->cancelled_at !== null
        );
    }

    /**
     * Check if campaign has failed messages
     */
    public function hasFailedMessages(): bool
    {
        return $this->messages()->where('status', \App\Enums\MessageStatus::is_failed)->exists();
    }

    /**
     * Update status and create history record
     */
    public function updateStatus(CampaignStatus $newStatus, ?string $reason = null, ?int $userId = null): void
    {
        $oldStatus = $this->getCurrentStatus();

        if ($oldStatus === $newStatus) {
            return; // No change needed
        }

        // Update the status
        $this->status = $newStatus;

        // Update boolean fields for backward compatibility
        $booleans = $newStatus->toBooleans();
        $this->is_sent = $booleans['is_sent'];
        $this->is_sending = $booleans['is_sending'];
        $this->is_scheduled = $booleans['is_scheduled'];

        // Update timestamp fields
        switch ($newStatus) {
            case CampaignStatus::CANCELLED:
                $this->cancelled_at = now();
                break;
            case CampaignStatus::FAILED:
                $this->failed_at = now();
                break;
            case CampaignStatus::COMPLETED:
                $this->sent_at = now();
                break;
        }

        $this->save();

        // Create history record
        CampaignStatusHistory::create([
            'campaign_id' => $this->id,
            'old_status' => $oldStatus->value,
            'new_status' => $newStatus->value,
            'reason' => $reason,
            'user_id' => $userId,
        ]);
    }
}
