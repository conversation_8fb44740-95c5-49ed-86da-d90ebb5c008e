<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Product extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'products';

    protected $fillable = [
        'organization_id',
        'brand_id',
        'name',
        'barcode',
        'description',
        'price',
        'unity',
        'last_priced_at'
    ];

    public function organization(){
        return $this->belongsTo(Organization::class);
    }
    public function brand(){
        return $this->belongsTo(Brand::class);
    }
    public function stock_entries(){
        return $this->hasMany(StockEntry::class);
    }
    public function stock_exits(){
        return $this->hasMany(StockExit::class);
    }
    public function batches(){
        return $this->hasMany(Batch::class);
    }
    public function histories(){
        return $this->hasMany(ProductHistory::class);
    }
    public function stocks(){
        return $this->hasMany(Stock::class);
    }

    public function groups(){
        return $this->belongsToMany(Group::class, 'groups_products');
    }
    public function projects(){
        return $this->belongsToMany(Project::class, 'projects_products');
    }
    public function budgets(){
        return $this->belongsToMany(Budget::class, 'budgets_products');
    }

}
