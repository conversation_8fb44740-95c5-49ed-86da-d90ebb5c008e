<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ExchangedMessage extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'exchanged_messages';

    protected $fillable = [
        'organization_id',
        'client_id',
        'phone_number_id',
        'conversation_id',
        'webhook_log_id',
        'message_id',
        'inbound',
        'outbound',
        'message',
        'json',
        'sent_at',
    ];

    protected $casts = [
        'inbound' => 'boolean',
        'outbound' => 'boolean',
        'json' => 'array',
        'sent_at' => 'datetime',
    ];

    public function organization()
    {
        return $this->belongsTo(Organization::class);
    }

    public function client()
    {
        return $this->belongsTo(Client::class);
    }

    public function phoneNumber()
    {
        return $this->belongsTo(PhoneNumber::class);
    }

    public function conversation()
    {
        return $this->belongsTo(Conversation::class);
    }

    public function webhookLog()
    {
        return $this->belongsTo(WhatsAppWebhookLog::class, 'webhook_log_id');
    }

    public function message()
    {
        return $this->belongsTo(Message::class);
    }
}
