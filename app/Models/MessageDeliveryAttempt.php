<?php

namespace App\Models;

use App\Enums\MessageStatus;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class MessageDeliveryAttempt extends Model
{
    use HasFactory;

    protected $table = 'message_delivery_attempts';

    protected $fillable = [
        'message_id',
        'attempt_number',
        'status',
        'error_message',
        'whatsapp_response_json',
        'attempted_at',
    ];

    protected $casts = [
        'status' => MessageStatus::class,
        'whatsapp_response_json' => 'array',
        'attempted_at' => 'datetime',
    ];

    public function message()
    {
        return $this->belongsTo(Message::class);
    }

    /**
     * Check if this attempt was successful
     */
    public function wasSuccessful(): bool
    {
        return $this->status === MessageStatus::is_sent;
    }

    /**
     * Check if this attempt failed
     */
    public function failed(): bool
    {
        return $this->status === MessageStatus::is_failed;
    }

    /**
     * Get the duration since this attempt
     */
    public function getDurationSinceAttempt(): int
    {
        return now()->diffInMinutes($this->attempted_at);
    }
}
