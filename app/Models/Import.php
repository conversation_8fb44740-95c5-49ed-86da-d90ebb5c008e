<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Import extends Model
{
    use SoftDeletes;

    protected $table = 'imports';

    protected $fillable = [
        'organization_id',
        'user_id',
        'model',
        'status',
        'header',
        'map',
        'is_processed',
        'file',
        'filename',
        'filesize',
        'filepath',
        'file_extension',
        'file_mime_type',
    ];

    public function organization(){
        return $this->belongsTo(Organization::class);
    }
    public function user(){
        return $this->belongsTo(User::class);
    }
}
