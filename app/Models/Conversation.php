<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Conversation extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'conversations';

    protected $fillable = [
        'organization_id',
        'user_id',
        'client_id',
        'flow_id',
        'phone_number_id',
        'current_step_id',
        'json',
        'is_finished',
    ];

    protected $casts = [
        'is_finished' => 'boolean',
    ];

    public function organization()
    {
        return $this->belongsTo(Organization::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function client()
    {
        return $this->belongsTo(Client::class);
    }

    public function flow()
    {
        return $this->belongsTo(Flow::class);
    }

    public function interactions()
    {
        return $this->hasMany(Interaction::class);
    }

    public function phoneNumber()
    {
        return $this->belongsTo(PhoneNumber::class);
    }

    public function currentStep()
    {
        return $this->belongsTo(Step::class, 'current_step_id');
    }
}
