<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class GroupProduct extends Model
{
    use HasFactory;
    protected $table = 'groups_products';

    protected $fillable = [
        'group_id',
        'product_id',
    ];

    public function product(){
        return $this->belongsTo(Product::class);
    }
    public function group(){
        return $this->belongsTo(Group::class);
    }
}
