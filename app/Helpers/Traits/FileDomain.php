<?php

namespace App\Helpers\Traits;

use Illuminate\Support\Facades\Storage;

trait FileDomain
{
    public function upload() : void {
        $meta = $this->fileHelper->getMeta();

        $this->filepath = self::FILEPATH . $this->organization_id;
        $this->filename = $meta["originalName"];
        $this->filesize = $meta["size"];
        $this->file_extension = $meta["originalExtension"];
        $this->file_mime_type = $meta["mimeType"];

        $this->file = $this->fileHelper->upload(self::FILEPATH . $this->organization_id);
    }

    public function getFilePath() : string {
        return Storage::disk('public')->path($this->file);
    }

    public function getRealPath() : string {
        return $this->fileHelper->getRealPath();
    }
}
