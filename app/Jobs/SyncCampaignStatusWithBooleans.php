<?php

namespace App\Jobs;

use App\Models\Campaign;
use App\Enums\CampaignStatus;
use App\Helpers\DBLog;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class SyncCampaignStatusWithBooleans implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $timeout = 300; // 5 minutes
    public int $tries = 3;

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            $syncedCount = 0;
            $inconsistentCount = 0;

            // Process campaigns in chunks to avoid memory issues
            Campaign::chunk(100, function ($campaigns) use (&$syncedCount, &$inconsistentCount) {
                foreach ($campaigns as $campaign) {
                    try {
                        $this->syncCampaignStatus($campaign, $syncedCount, $inconsistentCount);
                    } catch (\Throwable $e) {
                        DBLog::logError(
                            "Failed to sync campaign status: {$e->getMessage()}",
                            "SyncCampaignStatusWithBooleans",
                            $campaign->organization_id,
                            null,
                            ['campaign_id' => $campaign->id]
                        );
                    }
                }
            });

            DBLog::logInfo(
                "Campaign status sync completed",
                "SyncCampaignStatusWithBooleans",
                null,
                null,
                [
                    'synced_count' => $syncedCount,
                    'inconsistent_count' => $inconsistentCount
                ]
            );

        } catch (\Throwable $e) {
            DBLog::logError(
                "Campaign status sync job failed: {$e->getMessage()}",
                "SyncCampaignStatusWithBooleans",
                null,
                null
            );
            throw $e;
        }
    }

    private function syncCampaignStatus(Campaign $campaign, int &$syncedCount, int &$inconsistentCount): void
    {
        // Get current enum status
        $enumStatus = $campaign->status ?? CampaignStatus::DRAFT;

        // Derive status from boolean fields
        $derivedStatus = CampaignStatus::fromBooleans(
            $campaign->is_sent ?? false,
            $campaign->is_sending ?? false,
            $campaign->is_scheduled ?? false,
            $campaign->hasFailedMessages(),
            $campaign->cancelled_at !== null
        );

        // Check for inconsistency
        if ($enumStatus !== $derivedStatus) {
            $inconsistentCount++;

            // Log the inconsistency
            DBLog::logWarning(
                "Campaign status inconsistency detected",
                "SyncCampaignStatusWithBooleans",
                $campaign->organization_id,
                null,
                [
                    'campaign_id' => $campaign->id,
                    'enum_status' => $enumStatus->label(),
                    'derived_status' => $derivedStatus->label(),
                    'boolean_fields' => [
                        'is_sent' => $campaign->is_sent,
                        'is_sending' => $campaign->is_sending,
                        'is_scheduled' => $campaign->is_scheduled,
                        'cancelled_at' => $campaign->cancelled_at?->toISOString(),
                        'has_failed_messages' => $campaign->hasFailedMessages()
                    ]
                ]
            );

            // Decide which status to use as the source of truth
            $correctStatus = $this->resolveStatusConflict($enumStatus, $derivedStatus, $campaign);

            // Update the campaign with the correct status
            if ($correctStatus === $derivedStatus) {
                // Boolean fields are correct, update enum
                $campaign->status = $derivedStatus;
            } else {
                // Enum is correct, update boolean fields
                $booleans = $enumStatus->toBooleans();
                $campaign->is_sent = $booleans['is_sent'];
                $campaign->is_sending = $booleans['is_sending'];
                $campaign->is_scheduled = $booleans['is_scheduled'];
            }

            $campaign->save();
            $syncedCount++;
        }
    }

    private function resolveStatusConflict(
        CampaignStatus $enumStatus, 
        CampaignStatus $derivedStatus, 
        Campaign $campaign
    ): CampaignStatus {
        // Priority rules for conflict resolution:
        
        // 1. If enum is CANCELLED and cancelled_at is set, enum wins
        if ($enumStatus === CampaignStatus::CANCELLED && $campaign->cancelled_at) {
            return $enumStatus;
        }

        // 2. If enum is FAILED and failed_at is set, enum wins
        if ($enumStatus === CampaignStatus::FAILED && $campaign->failed_at) {
            return $enumStatus;
        }

        // 3. If boolean fields indicate SENDING and there are recent message activities, derived wins
        if ($derivedStatus === CampaignStatus::SENDING && $campaign->is_sending) {
            return $derivedStatus;
        }

        // 4. If enum is more recent (has status history), enum wins
        $latestStatusChange = $campaign->statusHistory()->latest()->first();
        if ($latestStatusChange && $latestStatusChange->created_at->isAfter(now()->subMinutes(10))) {
            return $enumStatus;
        }

        // 5. Default to derived status (boolean fields are more likely to be updated by external processes)
        return $derivedStatus;
    }
}
