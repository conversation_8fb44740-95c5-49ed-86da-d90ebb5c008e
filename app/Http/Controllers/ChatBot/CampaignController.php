<?php

namespace App\Http\Controllers\ChatBot;

use App\Domains\Filters\CampaignFilters;
use App\Domains\Filters\ClientFilters;
use App\Domains\Filters\OrderBy;
use App\Helpers\Traits\Response;
use App\Http\Controllers\Controller;
use App\Http\Requests\Campaign\StoreRequest;
use App\Http\Requests\Campaign\UpdateRequest;
use App\UseCases\ChatBot\Campaign\AddClientsToCampaign;
use App\UseCases\ChatBot\Campaign\Delete;
use App\UseCases\ChatBot\Campaign\Get;
use App\UseCases\ChatBot\Campaign\GetAll;
use App\UseCases\ChatBot\Campaign\GetByPhoneNumber;
use App\UseCases\ChatBot\Campaign\LaunchCampaign;
use App\UseCases\ChatBot\Campaign\RemoveClientFromCampaign;
use App\UseCases\ChatBot\Campaign\Store;
use App\UseCases\ChatBot\Campaign\Update;
use App\UseCases\Inventory\Client\GetByCampaign as GetClientsByCampaign;
use App\UseCases\ChatBot\Campaign\AssignCategories;
use App\UseCases\ChatBot\Campaign\AssignTags;
use App\UseCases\ChatBot\Campaign\Cancel;
use App\UseCases\ChatBot\Campaign\GetStatusHistory;
use App\Http\Requests\Campaign\AssignCategoriesRequest;
use App\Http\Requests\Campaign\AssignTagsRequest;
use Illuminate\Http\JsonResponse;

class CampaignController extends Controller
{
    use Response;

    public function index() : JsonResponse {
        try{
            /** @var GetAll $useCase */
            $useCase = app()->make(GetAll::class);
            $getAll = $useCase->perform(
                new CampaignFilters(request()->all()),
                new OrderBy(request()->only(OrderBy::ALLOWED_FIELDS))
            );

            $data = $getAll['data'] ?? [];
            unset($getAll['data']);

            return $this->response(
                "Authorized",
                "success",
                200 ,
                $data ?? [],
                null,
                $getAll ?? []
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    public function getByPhoneNumber(int $phone_number_id) : JsonResponse {
        try{
            /** @var GetByPhoneNumber $useCase */
            $useCase = app()->make(GetByPhoneNumber::class);
            $getAll = $useCase->perform(
                $phone_number_id,
                new CampaignFilters(request()->all()),
                new OrderBy(request()->only(OrderBy::ALLOWED_FIELDS))
            );

            $data = $getAll['data'] ?? [];
            unset($getAll['data']);

            return $this->response(
                "Authorized",
                "success",
                200 ,
                $data ?? [],
                null,
                $getAll ?? []
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    public function store(StoreRequest $request) : JsonResponse {
        try{
            /** @var Store $useCase */
            $useCase = app()->make(Store::class);
            $flow = $useCase->perform($request);

            return $this->response(
                "Campaign created successfully",
                "success",
                200 ,
                $flow->toArray()
            );
        } catch (\Throwable $e){
            return $this->errorResponse($e->getMessage());
        }
    }

    public function show(int $id) : JsonResponse {
        try{
            /** @var Get $useCase */
            $useCase = app()->make(Get::class);
            $data = $useCase->perform($id);

            return $this->response(
                "Authorized",
                "success",
                200 ,
                $data->toArray()
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    public function update(UpdateRequest $request, int $id) : JsonResponse {
        try{
            /** @var Update $useCase */
            $useCase = app()->make(Update::class);
            $update = $useCase->perform($request, $id);

            return $this->response(
                "Campaign updated successfully",
                "success",
                200 ,
                $update->toArray()
            );
        } catch (\Throwable $e){
            return $this->errorResponse($e->getMessage());
        }
    }

    public function destroy(int $id) : JsonResponse {
        try{
            /** @var Delete $useCase */
            $useCase = app()->make(Delete::class);
            $delete = $useCase->perform($id);

            return $this->response(
                "Campaign deleted successfully",
                "success",
                200 ,
                [$delete]
            );
        } catch (\Throwable $e){
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Add clients to a campaign
     */
    public function addClients(int $id) : JsonResponse {
        try{
            $client_ids = request()->input('client_ids', []);

            if (empty($client_ids) || !is_array($client_ids)) {
                return $this->errorResponse('client_ids array is required and cannot be empty');
            }

            /** @var AddClientsToCampaign $useCase */
            $useCase = app()->make(AddClientsToCampaign::class);
            $campaign = $useCase->perform($id, $client_ids);

            return $this->response(
                "Clients added to campaign successfully",
                "success",
                200,
                $campaign->toArray()
            );
        } catch (\Throwable $e){
            return $this->errorResponse($e->getMessage());
        }
    }

    public function removeClient(int $id) : JsonResponse {
        try {
            $client_id = request()->input('client_id');

            if (empty($client_id)) {
                return $this->errorResponse('client_id is required and cannot be empty');
            }

            /** @var RemoveClientFromCampaign $useCase */
            $useCase = app()->make(RemoveClientFromCampaign::class);
            $campaign = $useCase->perform($id, $client_id);

            return $this->response(
                "Client removed from campaign successfully",
                "success",
                200,
                $campaign->toArray()
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Launch a campaign
     */
    public function launch(int $id) : JsonResponse {
        try{
            $resend = request()->input('resend', false);

            /** @var LaunchCampaign $useCase */
            $useCase = app()->make(LaunchCampaign::class);
            $campaign = $useCase->perform($id, $resend);

            return $this->response(
                "Campaign launched successfully",
                "success",
                200,
                $campaign->toArray()
            );
        } catch (\Throwable $e){
            return $this->errorResponse($e->getMessage());
        }
    }

    public function getClients(int $id) : JsonResponse {
        try{
            /** @var GetClientsByCampaign $useCase */
            $useCase = app()->make(GetClientsByCampaign::class);
            $clients = $useCase->perform(
                $id,
                new ClientFilters(request()->all()),
                new OrderBy(request()->only(OrderBy::ALLOWED_FIELDS))
            );

            $data = $clients['data'] ?? [];
            unset($clients['data']);

            return $this->response(
                "Authorized",
                "success",
                200 ,
                $data ?? [],
                null,
                $clients ?? []
            );
        } catch (\Throwable $e){
            return $this->errorResponse($e->getMessage());
            }
    }

    /**
     * Assign categories to campaign
     */
    public function assignCategories(int $id, AssignCategoriesRequest $request): JsonResponse
    {
        try {
            /** @var AssignCategories $useCase */
            $useCase = app()->make(AssignCategories::class);

            $assignments = $useCase->execute(
                $id,
                $request->validated()['category_ids'],
                $request->user()->organization_id,
                $request->user()->id
            );

            return $this->response(
                "Categories assigned successfully",
                "success",
                200,
                array_map(fn($assignment) => $assignment->toArray(), $assignments)
            );

        } catch (\InvalidArgumentException $e) {
            return $this->errorResponse($e->getMessage(), 422);
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Assign tags to campaign
     */
    public function assignTags(int $id, AssignTagsRequest $request): JsonResponse
    {
        try {
            /** @var AssignTags $useCase */
            $useCase = app()->make(AssignTags::class);

            $assignments = $useCase->execute(
                $id,
                $request->validated()['tags'],
                $request->user()->organization_id,
                $request->user()->id
            );

            return $this->response(
                "Tags assigned successfully",
                "success",
                200,
                array_map(fn($assignment) => $assignment->toArray(), $assignments)
            );

        } catch (\InvalidArgumentException $e) {
            return $this->errorResponse($e->getMessage(), 422);
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Cancel campaign
     */
    public function cancel(int $id): JsonResponse
    {
        try {
            /** @var Cancel $useCase */
            $useCase = app()->make(Cancel::class);

            $reason = request()->input('reason', 'Campaign cancelled by user');

            $cancelled = $useCase->execute(
                $id,
                request()->user()->organization_id,
                request()->user()->id,
                $reason
            );

            if ($cancelled) {
                return $this->response(
                    "Campaign cancelled successfully",
                    "success",
                    200
                );
            }

            return $this->errorResponse("Failed to cancel campaign", 500);

        } catch (\InvalidArgumentException $e) {
            return $this->errorResponse($e->getMessage(), 422);
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Get campaign status history
     */
    public function statusHistory(int $id): JsonResponse
    {
        try {
            /** @var GetStatusHistory $useCase */
            $useCase = app()->make(GetStatusHistory::class);

            $limit = request()->query('limit', 50);

            $history = $useCase->execute(
                $id,
                request()->user()->organization_id,
                $limit
            );

            return $this->response(
                "Status history retrieved successfully",
                "success",
                200,
                $history
            );

        } catch (\InvalidArgumentException $e) {
            return $this->errorResponse($e->getMessage(), 422);
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Get campaign status timeline
     */
    public function statusTimeline(int $id): JsonResponse
    {
        try {
            /** @var GetStatusHistory $useCase */
            $useCase = app()->make(GetStatusHistory::class);

            $timeline = $useCase->getTimeline(
                $id,
                request()->user()->organization_id
            );

            return $this->response(
                "Status timeline retrieved successfully",
                "success",
                200,
                $timeline
            );

        } catch (\InvalidArgumentException $e) {
            return $this->errorResponse($e->getMessage(), 422);
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }
}
