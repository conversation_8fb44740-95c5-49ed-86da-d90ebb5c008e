<?php

namespace App\Http\Requests\ExchangedMessage;

use App\Helpers\Traits\Response;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;

class UpdateRequest extends FormRequest
{
    use Response;

    private const ERROR_STATUS = "error";
    private const ERROR_CODE = 422;
    private const ERROR_MESSAGE = "Validation Failed";

    public function authorize()
    {
        return true;
    }

    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(
            $this->response(
                self::ERROR_MESSAGE,
                self::ERROR_STATUS,
                self::ERROR_CODE,
                [],
                $validator->errors()->toArray()
            )
        );
    }

    public function rules()
    {
        return [
            'client_id' => 'nullable|exists:clients,id',
            'phone_number_id' => 'nullable|exists:phone_numbers,id',
            'conversation_id' => 'nullable|exists:conversations,id',
            'webhook_log_id' => 'nullable|exists:whatsapp_webhook_logs,id',
            'message_id' => 'nullable|exists:messages,id',
            'inbound' => 'boolean',
            'outbound' => 'boolean',
            'message' => 'nullable|string',
            'json' => 'nullable|array',
            'sent_at' => 'nullable|date',
        ];
    }

    public function messages()
    {
        return [
            'client_id.exists' => __('The selected client does not exist.'),
            'phone_number_id.exists' => __('The selected phone number does not exist.'),
            'conversation_id.exists' => __('The selected conversation does not exist.'),
            'webhook_log_id.exists' => __('The selected webhook log does not exist.'),
            'message_id.exists' => __('The selected message does not exist.'),
            'inbound.boolean' => __('The inbound field must be true or false.'),
            'outbound.boolean' => __('The outbound field must be true or false.'),
            'message.string' => __('The message field must be a string.'),
            'json.array' => __('The json field must be an array.'),
            'sent_at.date' => __('The sent at field must be a valid date.'),
        ];
    }
}
