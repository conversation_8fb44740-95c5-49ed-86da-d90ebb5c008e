<?php

namespace App\Http\Requests\Category;

use Illuminate\Foundation\Http\FormRequest;
use App\Domains\ChatBot\Category;

class UpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'name' => [
                'sometimes',
                'required',
                'string',
                'max:255',
                'min:2'
            ],
            'description' => [
                'sometimes',
                'nullable',
                'string',
                'max:1000'
            ],
            'color' => [
                'sometimes',
                'nullable',
                'string',
                'regex:/^#[0-9A-Fa-f]{6}$/'
            ],
            'type' => [
                'sometimes',
                'required',
                'string',
                'in:' . implode(',', Category::getAvailableTypes())
            ]
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'name.required' => 'Category name is required',
            'name.min' => 'Category name must be at least 2 characters',
            'name.max' => 'Category name cannot exceed 255 characters',
            'description.max' => 'Description cannot exceed 1000 characters',
            'color.regex' => 'Color must be a valid hex color code (e.g., #007bff)',
            'type.required' => 'Category type is required',
            'type.in' => 'Category type must be one of: ' . implode(', ', Category::getAvailableTypes())
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        $data = [];
        
        if ($this->has('name')) {
            $data['name'] = trim($this->name ?? '');
        }
        
        if ($this->has('description')) {
            $data['description'] = trim($this->description ?? '') ?: null;
        }
        
        if ($this->has('color')) {
            $data['color'] = $this->color;
        }
        
        if ($this->has('type')) {
            $data['type'] = $this->type;
        }

        $this->merge($data);
    }
}
