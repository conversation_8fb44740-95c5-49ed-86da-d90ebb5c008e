<?php

namespace App\Http\Requests\ASAAS\AsaasSubscription;

use Illuminate\Foundation\Http\FormRequest;

class UpdateAsaasSubscriptionRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'subscription_id' => 'sometimes|nullable|integer|exists:subscriptions,id',
            'asaas_customer_id' => 'sometimes|string|max:255',
            'asaas_subscription_id' => 'sometimes|string|max:255',
            'asaas_date_created' => 'sometimes|nullable|date',
            'asaas_synced_at' => 'sometimes|nullable|date',
            'asaas_sync_errors' => 'sometimes|nullable|array',
            'sync_status' => 'sometimes|nullable|in:pending,synced,error',
            'billing_type' => 'sometimes|nullable|in:BOLETO,CREDIT_CARD,PIX,UNDEFINED',
            'cycle' => 'sometimes|nullable|in:WEEKLY,BIWEEKLY,MONTHLY,BIMONTHLY,QUARTERLY,SEMIANNUALLY,YEARLY',
            'value' => 'sometimes|numeric|min:0',
            'next_due_date' => 'sometimes|nullable|date',
            'end_date' => 'sometimes|nullable|date',
            'description' => 'sometimes|nullable|string|max:500',
            'status' => 'sometimes|nullable|string|max:50',
            'max_payments' => 'sometimes|nullable|integer|min:1',
            'external_reference' => 'sometimes|nullable|string|max:255',
            'payment_link' => 'sometimes|nullable|url|max:500',
            'checkout_session' => 'sometimes|nullable|string|max:255',
            'discount_value' => 'sometimes|nullable|numeric|min:0',
            'discount_type' => 'sometimes|nullable|in:FIXED,PERCENTAGE',
            'discount_due_date_limit_days' => 'sometimes|nullable|integer|min:0',
            'fine_value' => 'sometimes|nullable|numeric|min:0',
            'fine_type' => 'sometimes|nullable|in:FIXED,PERCENTAGE',
            'interest_value' => 'sometimes|nullable|numeric|min:0',
            'credit_card_number' => 'sometimes|nullable|string|max:20',
            'credit_card_brand' => 'sometimes|nullable|string|max:50',
            'credit_card_token' => 'sometimes|nullable|string|max:255',
            'split_data' => 'sometimes|nullable|array',
            'deleted' => 'sometimes|nullable|boolean',
        ];
    }

    public function messages(): array
    {
        return [
            'subscription_id.exists' => 'Subscription not found',
            'value.numeric' => 'Value must be a number',
            'value.min' => 'Value must be greater than or equal to 0',
            'billing_type.in' => 'Billing type must be BOLETO, CREDIT_CARD, PIX, or UNDEFINED',
            'cycle.in' => 'Cycle must be WEEKLY, BIWEEKLY, MONTHLY, BIMONTHLY, QUARTERLY, SEMIANNUALLY, or YEARLY',
            'sync_status.in' => 'Sync status must be pending, synced, or error',
            'discount_type.in' => 'Discount type must be FIXED or PERCENTAGE',
            'fine_type.in' => 'Fine type must be FIXED or PERCENTAGE',
            'payment_link.url' => 'Payment link must be a valid URL',
        ];
    }
}
