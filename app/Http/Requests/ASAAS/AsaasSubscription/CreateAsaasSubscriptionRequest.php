<?php

namespace App\Http\Requests\ASAAS\AsaasSubscription;

use Illuminate\Foundation\Http\FormRequest;

class CreateAsaasSubscriptionRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'subscription_id' => 'nullable|integer|exists:subscriptions,id',
            'asaas_customer_id' => 'required|string|max:255',
            'asaas_subscription_id' => 'required|string|max:255',
            'asaas_date_created' => 'nullable|date',
            'asaas_synced_at' => 'nullable|date',
            'asaas_sync_errors' => 'nullable|array',
            'sync_status' => 'nullable|in:pending,synced,error',
            'billing_type' => 'nullable|in:BOLETO,CREDIT_CARD,PIX,UNDEFINED',
            'cycle' => 'nullable|in:WEEKLY,BIWEEKLY,MONTHLY,BIMONTHLY,QUARTERLY,SEMIANNUALLY,YEARLY',
            'value' => 'required|numeric|min:0',
            'next_due_date' => 'nullable|date',
            'end_date' => 'nullable|date',
            'description' => 'nullable|string|max:500',
            'status' => 'nullable|string|max:50',
            'max_payments' => 'nullable|integer|min:1',
            'external_reference' => 'nullable|string|max:255',
            'payment_link' => 'nullable|url|max:500',
            'checkout_session' => 'nullable|string|max:255',
            'discount_value' => 'nullable|numeric|min:0',
            'discount_type' => 'nullable|in:FIXED,PERCENTAGE',
            'discount_due_date_limit_days' => 'nullable|integer|min:0',
            'fine_value' => 'nullable|numeric|min:0',
            'fine_type' => 'nullable|in:FIXED,PERCENTAGE',
            'interest_value' => 'nullable|numeric|min:0',
            'credit_card_number' => 'nullable|string|max:20',
            'credit_card_brand' => 'nullable|string|max:50',
            'credit_card_token' => 'nullable|string|max:255',
            'split_data' => 'nullable|array',
            'deleted' => 'nullable|boolean',
        ];
    }

    public function messages(): array
    {
        return [
            'subscription_id.exists' => 'Subscription not found',
            'asaas_customer_id.required' => 'ASAAS customer ID is required',
            'asaas_subscription_id.required' => 'ASAAS subscription ID is required',
            'value.required' => 'Value is required',
            'value.numeric' => 'Value must be a number',
            'value.min' => 'Value must be greater than or equal to 0',
            'billing_type.in' => 'Billing type must be BOLETO, CREDIT_CARD, PIX, or UNDEFINED',
            'cycle.in' => 'Cycle must be WEEKLY, BIWEEKLY, MONTHLY, BIMONTHLY, QUARTERLY, SEMIANNUALLY, or YEARLY',
            'sync_status.in' => 'Sync status must be pending, synced, or error',
            'discount_type.in' => 'Discount type must be FIXED or PERCENTAGE',
            'fine_type.in' => 'Fine type must be FIXED or PERCENTAGE',
            'payment_link.url' => 'Payment link must be a valid URL',
        ];
    }
}
