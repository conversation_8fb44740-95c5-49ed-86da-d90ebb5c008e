<?php

namespace App\Http\Requests\Parameter;

use App\Helpers\Traits\Response;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;

class StoreRequest extends FormRequest
{
    use Response;

    private const ERROR_STATUS = "error";
    private const ERROR_CODE = 422;
    private const ERROR_MESSAGE = "Validation Failed";

    public function authorize()
    {
        return true;
    }

    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(
            $this->response(
                self::ERROR_MESSAGE,
                self::ERROR_STATUS,
                self::ERROR_CODE,
                [],
                $validator->errors()->toArray()
            )
        );
    }

    public function rules()
    {
        return [
            'organization_id' => 'nullable|integer',
            'campaign_id' => 'nullable|integer',
            'component_id' => 'nullable|integer',
            'type' => 'nullable|string',
            'value' => 'nullable|string',
            'placeholder' => 'nullable|string',
        ];
    }

    public function messages()
    {
        return [
            'organization_id.integer' => __('The organization ID must be an integer.'),
            'campaign_id.integer' => __('The campaign ID must be an integer.'),
            'component_id.integer' => __('The component ID must be an integer.'),
            'type.string' => __('The type must be a string.'),
            'value.string' => __('The value must be a string.'),
            'placeholder.string' => __('The placeholder must be a string.'),
        ];
    }
}
