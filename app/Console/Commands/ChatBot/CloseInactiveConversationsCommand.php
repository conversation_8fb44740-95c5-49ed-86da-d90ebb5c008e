<?php

namespace App\Console\Commands\ChatBot;

use App\UseCases\ChatBot\Conversation\CloseInactiveConversation;
use App\Factories\ChatBot\ConversationFactory;
use App\Models\Conversation;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class CloseInactiveConversationsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'chatbot:close-inactive-conversations 
                            {--dry-run : Run without making changes}
                            {--force : Force execution without confirmation}
                            {--minutes= : Override default 30 minutes threshold}
                            {--organization= : Process only specific organization}
                            {--limit= : Limit number of conversations to process (default: 1000)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Close ChatBot conversations that have been inactive for more than the specified time';

    private CloseInactiveConversation $closeInactiveConversationUseCase;
    private ConversationFactory $conversationFactory;

    public function __construct(
        CloseInactiveConversation $closeInactiveConversationUseCase,
        ConversationFactory $conversationFactory
    ) {
        parent::__construct();
        $this->closeInactiveConversationUseCase = $closeInactiveConversationUseCase;
        $this->conversationFactory = $conversationFactory;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $dryRun = $this->option('dry-run');
        $force = $this->option('force');
        $minutesThreshold = $this->option('minutes') ?? 30;
        $organizationId = $this->option('organization');
        $limit = $this->option('limit') ?? 1000;

        $this->info('ChatBot Inactive Conversations Closure');
        $this->info('=========================================');

        if ($dryRun) {
            $this->warn('DRY RUN MODE - No changes will be made');
        }

        // Display configuration
        $this->displayConfiguration($minutesThreshold, $organizationId, $limit, $dryRun);

        // Confirm action unless forced or dry run
        if (!$force && !$dryRun) {
            if (!$this->confirm('Do you want to proceed with closing inactive conversations?')) {
                $this->info('Operation cancelled');
                return Command::SUCCESS;
            }
        }

        // Start processing
        $this->info('Starting to process inactive conversations...');
        
        try {
            $result = $this->processInactiveConversations($minutesThreshold, $organizationId, $limit, $dryRun);
            $this->displayResults($result);
            
            return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->error('Process failed: ' . $e->getMessage());
            return Command::FAILURE;
        }
    }

    /**
     * Display current configuration
     */
    protected function displayConfiguration(int $minutesThreshold, ?int $organizationId, int $limit, bool $dryRun): void
    {
        $this->info('Configuration:');
        $this->line("  Minutes threshold: {$minutesThreshold} minutes");
        $this->line("  Organization filter: " . ($organizationId ? "ID {$organizationId}" : 'All organizations'));
        $this->line("  Processing limit: {$limit} conversations");
        $this->line("  Dry run: " . ($dryRun ? 'Yes' : 'No'));
        $this->line('');
    }

    /**
     * Process inactive conversations
     */
    protected function processInactiveConversations(int $minutesThreshold, ?int $organizationId, int $limit, bool $dryRun): array
    {
        $cutoffTime = Carbon::now()->subMinutes($minutesThreshold);
        
        $this->info("Looking for conversations without interactions since: {$cutoffTime->format('Y-m-d H:i:s')}");

        // Find conversations that might be inactive
        $candidateConversations = $this->findCandidateConversations($cutoffTime, $organizationId, $limit);
        
        $this->info("Found {$candidateConversations->count()} candidate conversations to check");

        $processed = 0;
        $closed = 0;
        $skipped = 0;
        $errors = 0;

        $progressBar = $this->output->createProgressBar($candidateConversations->count());
        $progressBar->start();

        foreach ($candidateConversations as $conversationModel) {
            try {
                $processed++;
                
                // Convert model to domain object
                $conversation = $this->conversationFactory->buildFromModel($conversationModel);
                
                if (!$dryRun) {
                    // Use the use case to check and close if inactive
                    $wasClosed = $this->closeInactiveConversationUseCase->perform($conversation);
                    
                    if ($wasClosed) {
                        $closed++;
                    } else {
                        $skipped++;
                    }
                } else {
                    // In dry run, just simulate the check
                    $this->simulateCheck($conversation) ? $closed++ : $skipped++;
                }
                
                $progressBar->advance();
                
            } catch (\Exception $e) {
                $errors++;
                $this->error("\nError processing conversation {$conversationModel->id}: " . $e->getMessage());
                $progressBar->advance();
            }
        }

        $progressBar->finish();
        $this->line('');

        return [
            'processed' => $processed,
            'closed' => $closed,
            'skipped' => $skipped,
            'errors' => $errors,
            'dry_run' => $dryRun
        ];
    }

    /**
     * Find conversations that are candidates for closure
     */
    protected function findCandidateConversations(Carbon $cutoffTime, ?int $organizationId, int $limit)
    {
        $query = Conversation::with(['flow'])
            ->where('is_finished', false)
            ->whereHas('flow') // Only conversations with flows
            ->where(function ($q) use ($cutoffTime) {
                // Conversations where the last interaction was before cutoff time
                // OR conversations with no interactions at all that are old enough
                $q->whereDoesntHave('interactions')
                  ->where('created_at', '<', $cutoffTime);
                
                $q->orWhereHas('interactions', function ($interactionQuery) use ($cutoffTime) {
                    $interactionQuery->where('created_at', '<', $cutoffTime);
                }, '=', 1) // Has exactly one interaction that's old
                ->whereDoesntHave('interactions', function ($interactionQuery) use ($cutoffTime) {
                    $interactionQuery->where('created_at', '>=', $cutoffTime);
                }); // And no recent interactions
            });

        if ($organizationId) {
            $query->where('organization_id', $organizationId);
        }

        return $query->orderBy('updated_at', 'asc')
                    ->limit($limit)
                    ->get();
    }

    /**
     * Simulate the check for dry run mode
     */
    protected function simulateCheck(\App\Domains\ChatBot\Conversation $conversation): bool
    {
        if ($conversation->is_finished || !$conversation->flow) {
            return false;
        }

        $inactivityMinutes = $conversation->flow->getTimeoutMinutes();
        $cutoffTime = Carbon::now()->subMinutes($inactivityMinutes);
        
        $lastActivityTime = $conversation->updated_at ?? $conversation->created_at ?? Carbon::now();
        
        return $lastActivityTime->isBefore($cutoffTime);
    }

    /**
     * Display processing results
     */
    protected function displayResults(array $result): void
    {
        $this->info('Processing completed!');
        $this->info('==================');
        
        if ($result['dry_run']) {
            $this->warn('DRY RUN RESULTS:');
        }
        
        $this->line("Conversations processed: {$result['processed']}");
        $this->line("Conversations closed: {$result['closed']}");
        $this->line("Conversations skipped: {$result['skipped']}");
        
        if ($result['errors'] > 0) {
            $this->error("Errors encountered: {$result['errors']}");
        }
        
        if ($result['closed'] > 0) {
            if ($result['dry_run']) {
                $this->warn("Would have closed {$result['closed']} conversations");
            } else {
                $this->info("Successfully closed {$result['closed']} inactive conversations");
            }
        }
        
        if ($result['skipped'] > 0) {
            $this->line("Skipped {$result['skipped']} conversations (still active or already finished)");
        }
    }
}
