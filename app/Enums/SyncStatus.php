<?php

namespace App\Enums;

enum SyncStatus: string
{
    case SUCCESS = 'success';
    case FAILED = 'failed';
    case PARTIAL = 'partial';

    /**
     * Get human readable label
     */
    public function label(): string
    {
        return match($this) {
            self::SUCCESS => 'Success',
            self::FAILED => 'Failed',
            self::PARTIAL => 'Partial',
        };
    }

    /**
     * Get color for UI
     */
    public function color(): string
    {
        return match($this) {
            self::SUCCESS => 'success',
            self::FAILED => 'danger',
            self::PARTIAL => 'warning',
        };
    }

    /**
     * Get description
     */
    public function description(): string
    {
        return match($this) {
            self::SUCCESS => 'Synchronization completed successfully',
            self::FAILED => 'Synchronization failed completely',
            self::PARTIAL => 'Synchronization completed with some errors',
        };
    }
}
